#!/usr/bin/env python3
# -*- coding: utf-8 -*-


# Colab环境检测和自动设置
try:
    import google.colab
    IN_COLAB = True
    print("🔍 检测到Colab环境")
except ImportError:
    IN_COLAB = False
    print("🔍 本地环境")

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from PIL import Image
import json
import logging
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ================================
# 1. 环境检查和依赖导入
# ================================

def setup_colab_environment():
    """设置Colab环境"""
    logger.info("设置Colab环境...")

    # 1. 挂载Google Drive
    try:
        from google.colab import drive
        drive.mount('/content/drive')
        logger.info("Google Drive挂载成功")
    except ImportError:
        logger.info("不在Colab环境中，跳过Drive挂载")
    except Exception as e:
        logger.warning(f"Drive挂载失败: {e}")

    # 2. 安装必要的依赖（基于MobileVLM requirements.txt）
    logger.info("安装依赖包...")
    import subprocess
    import sys

    # MobileVLM所需的特定版本依赖
    dependencies = [
        "torch==2.0.1",
        "torchvision==0.15.2",
        "transformers==4.33.1",
        "tokenizers==0.13.3",
        "accelerate==0.21.0",
        "sentencepiece==0.1.99",
        "einops==0.6.1",
        "timm==0.9.12",
        "pillow",
        "pandas",
        "numpy",
        "tqdm",
        "requests"
    ]

    for dep in dependencies:
        try:
            result = subprocess.run(f"pip install {dep}", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✓ 安装成功: {dep}")
            else:
                logger.warning(f"⚠ 安装失败: {dep}")
        except Exception as e:
            logger.warning(f"⚠ 安装异常: {dep} - {e}")

    # 3. 设置MobileVLM
    mobilevlm_path = "/content/MobileVLM"  # 假设用户已上传
    if os.path.exists(mobilevlm_path):
        logger.info("发现MobileVLM源码目录")
        sys.path.insert(0, mobilevlm_path)

        # 验证导入
        try:
            from mobilevlm.model.mobilevlm import load_pretrained_model
            logger.info("✓ MobileVLM导入成功")
            # 更新全局变量
            globals()['MOBILEVLM_AVAILABLE'] = True
        except ImportError as e:
            logger.warning(f"⚠ MobileVLM导入失败: {e}")
            globals()['MOBILEVLM_AVAILABLE'] = False
    else:
        logger.info("未找到MobileVLM源码，尝试从GitHub克隆...")
        try:
            result = subprocess.run(
                "git clone https://github.com/Meituan-AutoML/MobileVLM.git",
                shell=True, cwd="/content", capture_output=True, text=True
            )
            if result.returncode == 0:
                sys.path.insert(0, "/content/MobileVLM")
                from mobilevlm.model.mobilevlm import load_pretrained_model
                logger.info("✓ MobileVLM克隆并导入成功")
                globals()['MOBILEVLM_AVAILABLE'] = True
            else:
                logger.error(f"✗ 克隆失败: {result.stderr}")
                globals()['MOBILEVLM_AVAILABLE'] = False
        except Exception as e:
            logger.error(f"✗ MobileVLM设置失败: {e}")
            globals()['MOBILEVLM_AVAILABLE'] = False

    # 4. 设置环境变量
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'

    return True

def check_environment():
    """检查运行环境和依赖"""
    logger.info("检查运行环境...")

    # 检查CUDA
    if torch.cuda.is_available():
        logger.info(f"CUDA可用，设备数量: {torch.cuda.device_count()}")
        logger.info(f"当前设备: {torch.cuda.get_device_name()}")
    else:
        logger.warning("CUDA不可用，将使用CPU")

    # 检查MobileVLM
    try:
        from mobilevlm.model.mobilevlm import load_pretrained_model
        logger.info("MobileVLM导入成功")
    except ImportError as e:
        logger.error(f"MobileVLM导入失败: {e}")
        logger.info("正在尝试安装MobileVLM...")
        if not setup_colab_environment():
            return False

    # 检查transformers
    try:
        from transformers import AutoTokenizer, AutoModel, AutoProcessor
        logger.info("Transformers导入成功")
    except ImportError as e:
        logger.error(f"Transformers导入失败: {e}")
        return False

    return True

# 导入必要的库
try:
    from transformers import AutoTokenizer, AutoModel, AutoProcessor
    import requests
    logger.info("基础库导入成功")
except ImportError as e:
    logger.error(f"基础库导入失败: {e}")
    sys.exit(1)

# MobileVLM导入（可选）
try:
    from mobilevlm.model.mobilevlm import load_pretrained_model
    MOBILEVLM_AVAILABLE = True
    logger.info("MobileVLM导入成功")
except ImportError:
    MOBILEVLM_AVAILABLE = False
    logger.warning("MobileVLM不可用，将使用备用方案")

    # 定义备用的load_pretrained_model函数
    def load_pretrained_model(model_name, device="cpu"):
        from transformers import AutoTokenizer, AutoModel
        tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-medium")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        model = AutoModel.from_pretrained("microsoft/DialoGPT-medium")
        return tokenizer, model, None, None

# ================================
# 2. 配置类
# ================================

class Config:
    """训练配置"""
    # 数据路径 - 适配Colab环境
    DATA_PATH = "/content/drive/MyDrive/mobilevlm_internvl2_caption.csv"
    IMAGE_PATH = "/content/drive/MyDrive/small_train_df"  # 图像文件夹路径
    
    # 模型配置
    BASE_MODEL = "mtgv/MobileVLM-1.7B"  # MobileVLM 1.7B
    ASSISTANT_MODEL = "OpenGVLab/InternVL2-4B"  # InternVL2-4B
    
    # InternVL2 API配置（减少内存占用）
    USE_INTERNVL2_API = True
    INTERNVL2_API_URL = "https://api-inference.huggingface.co/models/OpenGVLab/InternVL2-4B"
    HUGGINGFACE_TOKEN = None  # 需要设置HuggingFace token
    
    # 训练参数 - 适配Colab资源限制
    BATCH_SIZE = 1  # 减小batch size适配内存
    LEARNING_RATE = 1e-5
    NUM_EPOCHS = 3
    MAX_LENGTH = 256
    IMAGE_SIZE = 224
    
    # 样本数量限制（快速测试）
    MAX_SAMPLES = 50  # 可根据需要调整
    
    # CO-LLM特定参数
    COLLABORATION_WEIGHT = 0.3  # 协作损失权重
    TEMPERATURE = 0.7  # 知识蒸馏温度
    ALPHA = 0.5  # 基础模型和助手模型的融合权重
    
    # 输出路径
    OUTPUT_DIR = "/content/co_llm_output"
    
    # 设备配置
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    DTYPE = torch.float16 if torch.cuda.is_available() else torch.float32

config = Config()

# ================================
# 3. 数据处理类
# ================================

class MultimodalDataset(Dataset):
    """多模态数据集类"""
    
    def __init__(self, data_path, image_path, tokenizer, max_length=512, max_samples=None):
        """
        初始化数据集

        Args:
            data_path: CSV数据文件路径
            image_path: 图像文件夹路径
            tokenizer: 分词器
            max_length: 最大序列长度
            max_samples: 最大样本数量
        """
        # 处理包含换行符的CSV文件
        try:
            # 使用quoting=csv.QUOTE_ALL来处理包含换行的字段
            import csv
            self.data = pd.read_csv(data_path, quoting=csv.QUOTE_ALL, skipinitialspace=True)
            logger.info(f"使用QUOTE_ALL模式读取CSV文件")
        except Exception as e:
            logger.warning(f"QUOTE_ALL模式读取失败: {e}，尝试其他方式")
            try:
                # 尝试使用不同的参数
                self.data = pd.read_csv(data_path, quotechar='"', escapechar='\\', skipinitialspace=True)
                logger.info(f"使用转义字符模式读取CSV文件")
            except Exception as e2:
                logger.warning(f"转义字符模式读取失败: {e2}，使用默认模式")
                # 最后尝试默认模式
                self.data = pd.read_csv(data_path)
        
        # 限制样本数量
        if max_samples:
            self.data = self.data.head(max_samples)
        
        self.image_path = image_path
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # 清理数据中的换行符和特殊字符
        self._clean_text_data()

        logger.info(f"加载数据集: {len(self.data)} 条样本")

    def _clean_text_data(self):
        """清理文本数据中的换行符和特殊字符"""
        text_columns = ['user_prompt', 'mobilevlm_merged_caption', 'internvl2_merged_caption']

        for col in text_columns:
            if col in self.data.columns:
                # 处理换行符和特殊字符
                self.data[col] = self.data[col].astype(str).apply(self._clean_text)

        logger.info("文本数据清理完成")

    def _clean_text(self, text):
        """清理单个文本字段"""
        if pd.isna(text) or text == 'nan':
            return ""

        # 转换为字符串
        text = str(text)

        # 替换换行符为空格
        text = text.replace('\n', ' ').replace('\r', ' ')

        # 处理特殊的换行标记
        text = text.replace('<0x0A>', ' ')

        # 移除多余的空格
        text = ' '.join(text.split())

        # 移除可能的引号问题
        text = text.strip('"').strip("'")

        return text
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        """获取单个样本"""
        row = self.data.iloc[idx]
        
        # 构建图像路径
        image_id = row['id']
        image_path = os.path.join(self.image_path, image_id)
        
        # 加载图像（如果存在）
        image = None
        if os.path.exists(image_path):
            try:
                image = Image.open(image_path).convert('RGB')
            except Exception as e:
                logger.warning(f"无法加载图像 {image_path}: {e}")
                # 创建占位图像
                image = Image.new('RGB', (224, 224), color='white')
        else:
            # 创建占位图像
            image = Image.new('RGB', (224, 224), color='white')
        
        # 获取文本数据（已经过清理）
        user_prompt = str(row['user_prompt']) if pd.notna(row['user_prompt']) else "请描述这张图片:"
        mobilevlm_caption = str(row.get('mobilevlm_merged_caption', '')) if pd.notna(row.get('mobilevlm_merged_caption', '')) else ""
        internvl2_caption = str(row.get('internvl2_merged_caption', '')) if pd.notna(row.get('internvl2_merged_caption', '')) else ""

        # 确保文本不为空
        if not mobilevlm_caption.strip():
            mobilevlm_caption = "这是一张图片的基础描述。"
        if not internvl2_caption.strip():
            internvl2_caption = "这是一张图片的详细分析。"
        
        return {
            'image': image,
            'image_id': image_id,
            'user_prompt': user_prompt,
            'mobilevlm_caption': mobilevlm_caption,
            'internvl2_caption': internvl2_caption
        }

def create_supervised_data(dataset):
    """
    步骤0: 创造监督学习的训练数据
    基于MobileVLM和InternVL2的输出创建多样化的监督数据
    """
    logger.info("创建监督学习训练数据...")
    
    supervised_data = []
    
    for i in range(len(dataset)):
        sample = dataset[i]
        
        # 1. 基础描述任务（使用MobileVLM风格）
        supervised_data.append({
            'image': sample['image'],
            'input_text': "请详细描述这张图片:",
            'target_text': sample['mobilevlm_caption'],
            'task_type': 'basic_description',
            'image_id': sample['image_id']
        })
        
        # 2. 增强描述任务（使用InternVL2风格）
        supervised_data.append({
            'image': sample['image'],
            'input_text': "请从多个角度分析这张图片:",
            'target_text': sample['internvl2_caption'],
            'task_type': 'enhanced_description',
            'image_id': sample['image_id']
        })
        
        # 3. 对比分析任务
        comparison_text = f"基础描述: {sample['mobilevlm_caption']}\n增强描述: {sample['internvl2_caption']}"
        supervised_data.append({
            'image': sample['image'],
            'input_text': "请比较不同模型对这张图片的描述:",
            'target_text': comparison_text,
            'task_type': 'comparison',
            'image_id': sample['image_id']
        })
        
        # 4. 协作式描述（结合两种风格）
        collaborative_text = f"综合分析: {sample['mobilevlm_caption']} 补充观察: {sample['internvl2_caption']}"
        supervised_data.append({
            'image': sample['image'],
            'input_text': "请提供全面的图像分析:",
            'target_text': collaborative_text,
            'task_type': 'collaborative',
            'image_id': sample['image_id']
        })
    
    logger.info(f"生成监督学习数据: {len(supervised_data)} 条")
    return supervised_data

# ================================
# 4. CO-LLM模型架构
# ================================

class COLLMModel(nn.Module):
    """
    CO-LLM协作式多模态模型
    结合MobileVLM v2 1.7B (base) 和 InternVL2-4B (assistant)
    """
    
    def __init__(self, config):
        super(COLLMModel, self).__init__()
        self.config = config
        self.device = config.DEVICE
        
        logger.info("初始化CO-LLM模型...")
        
        # 基础模型 - MobileVLM v2 1.7B
        logger.info("加载MobileVLM基础模型...")

        if MOBILEVLM_AVAILABLE:
            try:
                # 使用MobileVLM的标准加载方式
                self.tokenizer, self.base_model, self.image_processor, self.context_len = load_pretrained_model(
                    config.BASE_MODEL,
                    load_8bit=False,
                    load_4bit=False,
                    device_map="auto",
                    device=config.DEVICE
                )
                logger.info("✓ MobileVLM模型加载成功")

            except Exception as e:
                logger.warning(f"⚠ MobileVLM模型加载失败: {e}")
                logger.info("尝试使用CPU加载...")
                try:
                    self.tokenizer, self.base_model, self.image_processor, self.context_len = load_pretrained_model(
                        config.BASE_MODEL,
                        load_8bit=False,
                        load_4bit=False,
                        device_map="cpu",
                        device="cpu"
                    )
                    # 后续移动到目标设备
                    self.base_model = self.base_model.to(config.DEVICE)
                    logger.info("✓ MobileVLM模型CPU加载成功")
                except Exception as e2:
                    logger.error(f"✗ MobileVLM CPU加载也失败: {e2}")
                    globals()['MOBILEVLM_AVAILABLE'] = False

        # 备用方案：使用transformers模型
        if not MOBILEVLM_AVAILABLE:
            logger.info("使用备用模型方案...")
            try:
                from transformers import AutoTokenizer, AutoModel

                # 使用GPT2作为备用语言模型
                backup_model = "gpt2"
                self.tokenizer = AutoTokenizer.from_pretrained(backup_model)
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token

                self.base_model = AutoModel.from_pretrained(
                    backup_model,
                    torch_dtype=config.DTYPE
                ).to(config.DEVICE)

                # 创建简单的图像处理器占位符
                self.image_processor = None
                self.context_len = 1024

                logger.info("✓ 备用模型加载成功")

            except Exception as e2:
                logger.error(f"✗ 备用模型加载失败: {e2}")
                raise e2
        
        # 助手模型配置（API模式）
        if config.USE_INTERNVL2_API:
            logger.info("使用InternVL2 API调用模式")
            self.assistant_model = None
            self.api_url = config.INTERNVL2_API_URL
        else:
            logger.info("加载本地InternVL2模型...")
            try:
                self.assistant_tokenizer = AutoTokenizer.from_pretrained(config.ASSISTANT_MODEL)
                self.assistant_model = AutoModel.from_pretrained(
                    config.ASSISTANT_MODEL,
                    torch_dtype=config.DTYPE,
                    device_map="auto"
                )
            except Exception as e:
                logger.warning(f"本地InternVL2加载失败: {e}，切换到API模式")
                self.assistant_model = None
                self.api_url = config.INTERNVL2_API_URL
        
        # 获取模型维度
        try:
            self.hidden_size = self.base_model.config.hidden_size
        except:
            self.hidden_size = 2048  # MobileVLM默认值
        
        # 协作层 - 多头注意力机制
        self.collaboration_layer = nn.MultiheadAttention(
            embed_dim=self.hidden_size,
            num_heads=8,
            dropout=0.1,
            dtype=config.DTYPE,
            batch_first=True
        )
        
        # 特征融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(self.hidden_size * 2, self.hidden_size),
            nn.LayerNorm(self.hidden_size),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 输出投影层
        try:
            vocab_size = self.tokenizer.vocab_size
        except:
            vocab_size = 32000  # 默认词汇表大小
        
        self.output_projection = nn.Linear(self.hidden_size, vocab_size)
        
        logger.info(f"模型初始化完成，隐藏维度: {self.hidden_size}")

    def _call_internvl2_api(self, input_text, image=None):
        """调用InternVL2 API"""
        try:
            headers = {}
            if self.config.HUGGINGFACE_TOKEN:
                headers["Authorization"] = f"Bearer {self.config.HUGGINGFACE_TOKEN}"

            # 构建请求数据
            data = {"inputs": input_text}

            response = requests.post(self.api_url, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get('generated_text', '')
                return str(result)
            else:
                logger.warning(f"API调用失败: {response.status_code}")
                return ""
        except Exception as e:
            logger.warning(f"API调用异常: {e}")
            return ""

    def forward(self, input_ids, attention_mask, images=None, labels=None):
        """前向传播"""
        batch_size = input_ids.size(0)

        # 基础模型前向传播
        try:
            if images is not None:
                base_outputs = self.base_model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    images=images
                )
            else:
                base_outputs = self.base_model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )

            # 获取隐藏状态
            if hasattr(base_outputs, 'last_hidden_state'):
                base_hidden = base_outputs.last_hidden_state
            else:
                base_hidden = base_outputs[0]

        except Exception as e:
            logger.warning(f"基础模型前向传播失败: {e}")
            # 创建默认隐藏状态
            seq_len = input_ids.size(1)
            base_hidden = torch.randn(batch_size, seq_len, self.hidden_size,
                                    dtype=self.config.DTYPE, device=self.device)

        # 助手模型输出（简化处理）
        if self.assistant_model is None:
            # 使用API或创建默认输出
            assistant_hidden = torch.randn_like(base_hidden)
        else:
            try:
                with torch.no_grad():
                    assistant_outputs = self.assistant_model(
                        input_ids=input_ids,
                        attention_mask=attention_mask
                    )
                    if hasattr(assistant_outputs, 'last_hidden_state'):
                        assistant_hidden = assistant_outputs.last_hidden_state
                    else:
                        assistant_hidden = assistant_outputs[0]
            except Exception as e:
                logger.warning(f"助手模型调用失败: {e}")
                assistant_hidden = torch.randn_like(base_hidden)

        # 协作层处理
        try:
            # 多头注意力协作
            collaborated_hidden, attention_weights = self.collaboration_layer(
                base_hidden, assistant_hidden, assistant_hidden
            )

            # 特征融合
            fused_features = torch.cat([base_hidden, collaborated_hidden], dim=-1)
            final_hidden = self.fusion_layer(fused_features)

        except Exception as e:
            logger.warning(f"协作层处理失败: {e}")
            final_hidden = base_hidden

        # 输出预测
        logits = self.output_projection(final_hidden)

        # 计算损失
        loss = None
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss(ignore_index=-100)

            # 调整维度匹配
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()

            loss = loss_fct(
                shift_logits.view(-1, shift_logits.size(-1)),
                shift_labels.view(-1)
            )

        return {
            'loss': loss,
            'logits': logits,
            'base_hidden': base_hidden,
            'collaborated_hidden': final_hidden
        }

# ================================
# 5. 训练器类
# ================================

class COLLMTrainer:
    """CO-LLM训练器"""

    def __init__(self, config):
        self.config = config
        self.device = config.DEVICE

        # 初始化模型
        self.model = COLLMModel(config).to(self.device)
        self.tokenizer = self.model.tokenizer
        self.image_processor = self.model.image_processor

        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.LEARNING_RATE,
            weight_decay=0.01
        )

        # 创建输出目录
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)

        logger.info("训练器初始化完成")

    def collate_fn(self, batch):
        """数据整理函数"""
        images = []
        input_texts = []
        target_texts = []

        for item in batch:
            images.append(item['image'])
            input_texts.append(item['input_text'])
            target_texts.append(item['target_text'])

        # 处理图像 - 兼容MobileVLM的处理方式
        try:
            if self.image_processor and MOBILEVLM_AVAILABLE:
                # 使用MobileVLM的图像处理方式
                from mobilevlm.utils import process_images
                pixel_values = process_images(images, self.image_processor, self.model.config)
                if pixel_values.dim() == 3:
                    pixel_values = pixel_values.unsqueeze(0)  # 添加batch维度
            elif self.image_processor:
                # 使用标准的transformers处理方式
                pixel_values = self.image_processor(
                    images=images,
                    return_tensors="pt"
                )['pixel_values']
            else:
                # 创建占位符
                pixel_values = torch.randn(len(images), 3, 224, 224)
        except Exception as e:
            logger.warning(f"图像处理失败: {e}")
            pixel_values = torch.randn(len(images), 3, 224, 224)

        # 处理文本
        inputs = self.tokenizer(
            input_texts,
            padding=True,
            truncation=True,
            max_length=self.config.MAX_LENGTH,
            return_tensors="pt"
        )

        targets = self.tokenizer(
            target_texts,
            padding=True,
            truncation=True,
            max_length=self.config.MAX_LENGTH,
            return_tensors="pt"
        )

        return {
            'input_ids': inputs['input_ids'],
            'attention_mask': inputs['attention_mask'],
            'pixel_values': pixel_values,
            'labels': targets['input_ids']
        }

    def step1_supervised_initialization(self, supervised_data):
        """
        步骤1: 监督学习初始化参数
        使用监督数据初始化模型参数
        """
        logger.info("开始监督学习初始化...")

        # 创建数据加载器
        dataloader = DataLoader(
            supervised_data,
            batch_size=self.config.BATCH_SIZE,
            shuffle=True,
            collate_fn=self.collate_fn
        )

        self.model.train()
        total_loss = 0
        num_batches = 0

        for epoch in range(self.config.NUM_EPOCHS):
            epoch_loss = 0

            for batch_idx, batch in enumerate(tqdm(dataloader, desc=f"监督训练 Epoch {epoch+1}")):
                # 移动到设备
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                pixel_values = batch['pixel_values'].to(self.device)
                labels = batch['labels'].to(self.device)

                # 前向传播
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    images=pixel_values,
                    labels=labels
                )

                loss = outputs['loss']

                # 反向传播
                self.optimizer.zero_grad()
                loss.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()

                epoch_loss += loss.item()
                total_loss += loss.item()
                num_batches += 1

                # 日志记录
                if batch_idx % 10 == 0:
                    logger.info(f"Epoch {epoch+1}, Batch {batch_idx}, Loss: {loss.item():.4f}")

            avg_epoch_loss = epoch_loss / len(dataloader)
            logger.info(f"Epoch {epoch+1} 完成，平均损失: {avg_epoch_loss:.4f}")

        # 保存监督学习模型
        supervised_model_path = os.path.join(self.config.OUTPUT_DIR, "supervised_model.pth")
        torch.save(self.model.state_dict(), supervised_model_path)
        logger.info(f"监督学习模型已保存: {supervised_model_path}")

        avg_total_loss = total_loss / num_batches
        logger.info(f"监督学习完成，平均损失: {avg_total_loss:.4f}")

        return avg_total_loss

    def step2_unsupervised_training(self, dataset):
        """
        步骤2: 无监督学习训练模型
        基于CO-LLM思想的协作式无监督训练
        """
        logger.info("开始无监督协作训练...")

        # 创建无监督数据加载器
        def unsupervised_collate_fn(batch):
            images = [item['image'] for item in batch]
            prompts = [item['user_prompt'] for item in batch]

            # 处理图像
            try:
                pixel_values = self.image_processor(
                    images=images,
                    return_tensors="pt"
                )['pixel_values']
            except:
                pixel_values = torch.randn(len(images), 3, 224, 224)

            # 处理文本
            inputs = self.tokenizer(
                prompts,
                padding=True,
                truncation=True,
                max_length=self.config.MAX_LENGTH,
                return_tensors="pt"
            )

            return {
                'input_ids': inputs['input_ids'],
                'attention_mask': inputs['attention_mask'],
                'pixel_values': pixel_values,
                'prompts': prompts,
                'images': images
            }

        dataloader = DataLoader(
            dataset,
            batch_size=self.config.BATCH_SIZE,
            shuffle=True,
            collate_fn=unsupervised_collate_fn
        )

        self.model.train()

        for epoch in range(self.config.NUM_EPOCHS):
            for batch_idx, batch in enumerate(tqdm(dataloader, desc=f"无监督训练 Epoch {epoch+1}")):
                # 移动到设备
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                pixel_values = batch['pixel_values'].to(self.device)

                # 协作式自监督学习
                with torch.no_grad():
                    # 获取基础模型和协作模型的输出
                    outputs = self.model(
                        input_ids=input_ids,
                        attention_mask=attention_mask,
                        images=pixel_values
                    )

                    base_hidden = outputs['base_hidden']
                    collaborated_hidden = outputs['collaborated_hidden']

                # 计算协作损失（对比学习）
                # 使用余弦相似度作为协作信号
                base_norm = torch.nn.functional.normalize(base_hidden, dim=-1)
                collab_norm = torch.nn.functional.normalize(collaborated_hidden, dim=-1)

                # 协作损失：鼓励协作特征与基础特征的适度差异
                collaboration_loss = 1.0 - torch.mean(torch.sum(base_norm * collab_norm, dim=-1))

                # 一致性损失：保持语义一致性
                consistency_loss = torch.nn.functional.mse_loss(
                    base_hidden.mean(dim=1),
                    collaborated_hidden.mean(dim=1)
                )

                # 总损失
                total_loss = (
                    self.config.COLLABORATION_WEIGHT * collaboration_loss +
                    (1 - self.config.COLLABORATION_WEIGHT) * consistency_loss
                )

                # 反向传播
                self.optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                # 日志记录
                if batch_idx % 10 == 0:
                    logger.info(f"无监督 Epoch {epoch+1}, Batch {batch_idx}, "
                              f"协作损失: {collaboration_loss.item():.4f}, "
                              f"一致性损失: {consistency_loss.item():.4f}")

        # 保存无监督训练模型
        unsupervised_model_path = os.path.join(self.config.OUTPUT_DIR, "unsupervised_model.pth")
        torch.save(self.model.state_dict(), unsupervised_model_path)
        logger.info(f"无监督训练模型已保存: {unsupervised_model_path}")

        logger.info("无监督协作训练完成")

# ================================
# 6. 推理类
# ================================

class COLLMInference:
    """CO-LLM推理类"""

    def __init__(self, config, model_path=None):
        self.config = config
        self.device = config.DEVICE

        # 初始化模型
        self.model = COLLMModel(config).to(self.device)
        self.tokenizer = self.model.tokenizer
        self.image_processor = self.model.image_processor

        # 加载训练好的模型
        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            logger.info(f"加载模型: {model_path}")

        self.model.eval()

    def generate_response(self, image, prompt, max_length=256):
        """生成响应"""
        with torch.no_grad():
            # 处理图像
            if isinstance(image, str):
                image = Image.open(image).convert('RGB')

            try:
                pixel_values = self.image_processor(
                    images=[image],
                    return_tensors="pt"
                )['pixel_values'].to(self.device)
            except:
                pixel_values = torch.randn(1, 3, 224, 224, device=self.device)

            # 处理文本
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=max_length
            )

            input_ids = inputs['input_ids'].to(self.device)
            attention_mask = inputs['attention_mask'].to(self.device)

            # 生成响应
            try:
                # 使用模型的生成能力
                if hasattr(self.model.base_model, 'generate'):
                    generated_ids = self.model.base_model.generate(
                        input_ids=input_ids,
                        images=pixel_values,
                        max_length=max_length,
                        do_sample=True,
                        temperature=0.7,
                        pad_token_id=self.tokenizer.pad_token_id,
                        eos_token_id=self.tokenizer.eos_token_id
                    )

                    # 解码生成的文本
                    response = self.tokenizer.decode(
                        generated_ids[0][input_ids.shape[1]:],
                        skip_special_tokens=True
                    )
                else:
                    # 使用前向传播获取logits
                    outputs = self.model(
                        input_ids=input_ids,
                        attention_mask=attention_mask,
                        images=pixel_values
                    )

                    # 简单的贪婪解码
                    logits = outputs['logits']
                    next_token_id = torch.argmax(logits[0, -1, :], dim=-1)
                    response = self.tokenizer.decode([next_token_id.item()], skip_special_tokens=True)

            except Exception as e:
                logger.warning(f"生成失败: {e}")
                response = "抱歉，生成响应时出现错误。"

            return response.strip()

    def collaborative_generate(self, image, prompt, max_length=256):
        """协作式生成（结合两个模型的优势）"""
        with torch.no_grad():
            # 处理输入
            if isinstance(image, str):
                image = Image.open(image).convert('RGB')

            try:
                pixel_values = self.image_processor(
                    images=[image],
                    return_tensors="pt"
                )['pixel_values'].to(self.device)
            except:
                pixel_values = torch.randn(1, 3, 224, 224, device=self.device)

            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=max_length
            )

            input_ids = inputs['input_ids'].to(self.device)
            attention_mask = inputs['attention_mask'].to(self.device)

            # 获取协作式隐藏状态
            outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                images=pixel_values
            )

            # 基于协作隐藏状态生成
            collaborated_logits = outputs['logits']

            # 简单的贪婪解码
            generated_tokens = []
            current_ids = input_ids

            for _ in range(min(50, max_length - input_ids.shape[1])):  # 限制生成长度
                next_token_logits = collaborated_logits[0, -1, :]
                next_token_id = torch.argmax(next_token_logits, dim=-1).unsqueeze(0).unsqueeze(0)

                if next_token_id.item() == self.tokenizer.eos_token_id:
                    break

                generated_tokens.append(next_token_id.item())
                current_ids = torch.cat([current_ids, next_token_id], dim=1)

                # 更新logits（可选，为了简化可以跳过）
                if len(generated_tokens) < 10:  # 只更新前几个token
                    try:
                        outputs = self.model(
                            input_ids=current_ids,
                            attention_mask=torch.ones_like(current_ids),
                            images=pixel_values
                        )
                        collaborated_logits = outputs['logits']
                    except:
                        break

            # 解码响应
            if generated_tokens:
                response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
            else:
                response = "无法生成响应"

            return response.strip()

# ================================
# 7. 主函数
# ================================

def main():
    """主训练流程"""
    logger.info("=" * 50)
    logger.info("CO-LLM: MobileVLM v2 1.7B + InternVL2-4B 训练框架")
    logger.info("=" * 50)

    # 检查环境
    if not check_environment():
        logger.error("环境检查失败，退出程序")
        return

    # 检查数据文件
    if not os.path.exists(config.DATA_PATH):
        logger.error(f"数据文件不存在: {config.DATA_PATH}")
        logger.info("请确保数据文件路径正确")
        return

    logger.info(f"使用设备: {config.DEVICE}")
    logger.info(f"数据类型: {config.DTYPE}")
    logger.info(f"批次大小: {config.BATCH_SIZE}")
    logger.info(f"最大样本数: {config.MAX_SAMPLES}")

    try:
        # 初始化数据集
        logger.info("初始化数据集...")

        # 创建临时tokenizer用于数据集初始化
        from transformers import AutoTokenizer
        temp_tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-medium")
        if temp_tokenizer.pad_token is None:
            temp_tokenizer.pad_token = temp_tokenizer.eos_token

        dataset = MultimodalDataset(
            config.DATA_PATH,
            config.IMAGE_PATH,
            temp_tokenizer,
            config.MAX_LENGTH,
            config.MAX_SAMPLES
        )

        # 步骤0: 创建监督学习数据
        logger.info("步骤0: 创建监督学习数据")
        supervised_data = create_supervised_data(dataset)

        # 初始化训练器
        logger.info("初始化训练器...")
        trainer = COLLMTrainer(config)

        # 步骤1: 监督学习初始化
        logger.info("步骤1: 监督学习初始化")
        supervised_loss = trainer.step1_supervised_initialization(supervised_data)

        # 步骤2: 无监督学习训练
        logger.info("步骤2: 无监督学习训练")
        trainer.step2_unsupervised_training(dataset)

        logger.info("训练完成!")

        # 步骤3: 推理示例
        logger.info("步骤3: 推理示例")

        # 加载训练好的模型进行推理
        model_path = os.path.join(config.OUTPUT_DIR, "unsupervised_model.pth")
        inference = COLLMInference(config, model_path)

        # 示例推理
        if len(dataset) > 0:
            sample = dataset[0]

            logger.info("基础推理示例:")
            response = inference.generate_response(
                sample['image'],
                sample['user_prompt']
            )
            logger.info(f"输入: {sample['user_prompt']}")
            logger.info(f"输出: {response}")

            logger.info("协作式推理示例:")
            collaborative_response = inference.collaborative_generate(
                sample['image'],
                sample['user_prompt']
            )
            logger.info(f"协作输出: {collaborative_response}")

        logger.info("所有步骤完成!")

    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

# ================================
# 8. Notebook友好的运行函数
# ================================

def run_training_pipeline():
    """
    Notebook友好的训练流程
    可以在Jupyter/Colab中直接调用
    """
    try:
        return main()
    except Exception as e:
        logger.error(f"训练流程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_setup():
    """快速环境设置"""
    logger.info("=" * 50)
    logger.info("CO-LLM快速环境设置")
    logger.info("=" * 50)

    # 设置环境
    if not setup_colab_environment():
        logger.error("环境设置失败")
        return False

    # 检查环境
    if not check_environment():
        logger.error("环境检查失败")
        return False

    logger.info("环境设置完成！可以运行训练了")
    return True

def test_mobilevlm_loading():
    """测试MobileVLM加载"""
    logger.info("=" * 50)
    logger.info("测试MobileVLM模型加载")
    logger.info("=" * 50)

    try:
        config = Config()
        config.MAX_SAMPLES = 1  # 最小测试

        # 测试模型初始化
        model = COLLMModel(config)
        logger.info("✓ 模型初始化成功")

        # 测试简单前向传播
        batch_size = 1
        seq_len = 10

        input_ids = torch.randint(0, 1000, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        images = torch.randn(batch_size, 3, 224, 224)

        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                images=images
            )

        logger.info("✓ 前向传播测试成功")
        logger.info(f"输出形状: {outputs['logits'].shape}")
        return True

    except Exception as e:
        logger.error(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

# ================================
# 9. 主程序入口
# ================================

if __name__ == "__main__":
    main()

# Notebook中可以直接调用的函数
# quick_setup()  # 设置环境
# run_training_pipeline()  # 运行训练
