


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>1: Inference and train with existing models and standard datasets &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="2: Train with customized datasets" href="2_new_data_model.html" />
  <link rel="prev" title="Benchmark and Model Zoo" href="model_zoo.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>1: Inference and train with existing models and standard datasets</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/1_exist_data_model.md.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="inference-and-train-with-existing-models-and-standard-datasets">
<h1>1: Inference and train with existing models and standard datasets<a class="headerlink" href="#inference-and-train-with-existing-models-and-standard-datasets" title="Permalink to this heading">¶</a></h1>
<p>MMDetection provides hundreds of existing and existing detection models in <a class="reference external" href="https://rsidetection.readthedocs.io/en/latest/model_zoo.html">Model Zoo</a>), and supports multiple standard datasets, including Pascal VOC, COCO, CityScapes, LVIS, etc. This note will show how to perform common tasks on these existing models and standard datasets, including:</p>
<ul class="simple">
<li><p>Use existing models to inference on given images.</p></li>
<li><p>Test existing models on standard datasets.</p></li>
<li><p>Train predefined models on standard datasets.</p></li>
</ul>
<section id="inference-with-existing-models">
<h2>Inference with existing models<a class="headerlink" href="#inference-with-existing-models" title="Permalink to this heading">¶</a></h2>
<p>By inference, we mean using trained models to detect objects on images. In MMDetection, a model is defined by a configuration file and existing model parameters are save in a checkpoint file.</p>
<p>To start with, we recommend <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/faster_rcnn">Faster RCNN</a> with this <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py">configuration file</a> and this <a class="reference external" href="https://download.openmmlab.com/rsidetection/v2.0/faster_rcnn/faster_rcnn_r50_fpn_1x_coco/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth">checkpoint file</a>. It is recommended to download the checkpoint file to <code class="docutils literal notranslate"><span class="pre">checkpoints</span></code> directory.</p>
<section id="high-level-apis-for-inference">
<h3>High-level APIs for inference<a class="headerlink" href="#high-level-apis-for-inference" title="Permalink to this heading">¶</a></h3>
<p>MMDetection provide high-level Python APIs for inference on images. Here is an example of building the model and inference on given images or videos.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">rsidet.apis</span> <span class="kn">import</span> <span class="n">init_detector</span><span class="p">,</span> <span class="n">inference_detector</span>
<span class="kn">import</span> <span class="nn">mmcv</span>

<span class="c1"># Specify the path to model config and checkpoint file</span>
<span class="n">config_file</span> <span class="o">=</span> <span class="s1">&#39;configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py&#39;</span>
<span class="n">checkpoint_file</span> <span class="o">=</span> <span class="s1">&#39;checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth&#39;</span>

<span class="c1"># build the model from a config file and a checkpoint file</span>
<span class="n">model</span> <span class="o">=</span> <span class="n">init_detector</span><span class="p">(</span><span class="n">config_file</span><span class="p">,</span> <span class="n">checkpoint_file</span><span class="p">,</span> <span class="n">device</span><span class="o">=</span><span class="s1">&#39;cuda:0&#39;</span><span class="p">)</span>

<span class="c1"># test a single image and show the results</span>
<span class="n">img</span> <span class="o">=</span> <span class="s1">&#39;test.jpg&#39;</span>  <span class="c1"># or img = mmcv.imread(img), which will only load it once</span>
<span class="n">result</span> <span class="o">=</span> <span class="n">inference_detector</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">img</span><span class="p">)</span>
<span class="c1"># visualize the results in a new window</span>
<span class="n">model</span><span class="o">.</span><span class="n">show_result</span><span class="p">(</span><span class="n">img</span><span class="p">,</span> <span class="n">result</span><span class="p">)</span>
<span class="c1"># or save the visualization results to image files</span>
<span class="n">model</span><span class="o">.</span><span class="n">show_result</span><span class="p">(</span><span class="n">img</span><span class="p">,</span> <span class="n">result</span><span class="p">,</span> <span class="n">out_file</span><span class="o">=</span><span class="s1">&#39;result.jpg&#39;</span><span class="p">)</span>

<span class="c1"># test a video and show the results</span>
<span class="n">video</span> <span class="o">=</span> <span class="n">mmcv</span><span class="o">.</span><span class="n">VideoReader</span><span class="p">(</span><span class="s1">&#39;video.mp4&#39;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">frame</span> <span class="ow">in</span> <span class="n">video</span><span class="p">:</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">inference_detector</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">frame</span><span class="p">)</span>
    <span class="n">model</span><span class="o">.</span><span class="n">show_result</span><span class="p">(</span><span class="n">frame</span><span class="p">,</span> <span class="n">result</span><span class="p">,</span> <span class="n">wait_time</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>A notebook demo can be found in <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/demo/inference_demo.ipynb">demo/inference_demo.ipynb</a>.</p>
<p>Note:  <code class="docutils literal notranslate"><span class="pre">inference_detector</span></code> only supports single-image inference for now.</p>
</section>
<section id="asynchronous-interface-supported-for-python-3-7">
<h3>Asynchronous interface - supported for Python 3.7+<a class="headerlink" href="#asynchronous-interface-supported-for-python-3-7" title="Permalink to this heading">¶</a></h3>
<p>For Python 3.7+, MMDetection also supports async interfaces.
By utilizing CUDA streams, it allows not to block CPU on GPU bound inference code and enables better CPU/GPU utilization for single-threaded application. Inference can be done concurrently either between different input data samples or between different models of some inference pipeline.</p>
<p>See <code class="docutils literal notranslate"><span class="pre">tests/async_benchmark.py</span></code> to compare the speed of synchronous and asynchronous interfaces.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">asyncio</span>
<span class="kn">import</span> <span class="nn">torch</span>
<span class="kn">from</span> <span class="nn">rsidet.apis</span> <span class="kn">import</span> <span class="n">init_detector</span><span class="p">,</span> <span class="n">async_inference_detector</span>
<span class="kn">from</span> <span class="nn">rsidet.utils.contextmanagers</span> <span class="kn">import</span> <span class="n">concurrent</span>

<span class="n">async</span> <span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
    <span class="n">config_file</span> <span class="o">=</span> <span class="s1">&#39;configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py&#39;</span>
    <span class="n">checkpoint_file</span> <span class="o">=</span> <span class="s1">&#39;checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth&#39;</span>
    <span class="n">device</span> <span class="o">=</span> <span class="s1">&#39;cuda:0&#39;</span>
    <span class="n">model</span> <span class="o">=</span> <span class="n">init_detector</span><span class="p">(</span><span class="n">config_file</span><span class="p">,</span> <span class="n">checkpoint</span><span class="o">=</span><span class="n">checkpoint_file</span><span class="p">,</span> <span class="n">device</span><span class="o">=</span><span class="n">device</span><span class="p">)</span>

    <span class="c1"># queue is used for concurrent inference of multiple images</span>
    <span class="n">streamqueue</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">Queue</span><span class="p">()</span>
    <span class="c1"># queue size defines concurrency level</span>
    <span class="n">streamqueue_size</span> <span class="o">=</span> <span class="mi">3</span>

    <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">streamqueue_size</span><span class="p">):</span>
        <span class="n">streamqueue</span><span class="o">.</span><span class="n">put_nowait</span><span class="p">(</span><span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">Stream</span><span class="p">(</span><span class="n">device</span><span class="o">=</span><span class="n">device</span><span class="p">))</span>

    <span class="c1"># test a single image and show the results</span>
    <span class="n">img</span> <span class="o">=</span> <span class="s1">&#39;test.jpg&#39;</span>  <span class="c1"># or img = mmcv.imread(img), which will only load it once</span>

    <span class="n">async</span> <span class="k">with</span> <span class="n">concurrent</span><span class="p">(</span><span class="n">streamqueue</span><span class="p">):</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">await</span> <span class="n">async_inference_detector</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">img</span><span class="p">)</span>

    <span class="c1"># visualize the results in a new window</span>
    <span class="n">model</span><span class="o">.</span><span class="n">show_result</span><span class="p">(</span><span class="n">img</span><span class="p">,</span> <span class="n">result</span><span class="p">)</span>
    <span class="c1"># or save the visualization results to image files</span>
    <span class="n">model</span><span class="o">.</span><span class="n">show_result</span><span class="p">(</span><span class="n">img</span><span class="p">,</span> <span class="n">result</span><span class="p">,</span> <span class="n">out_file</span><span class="o">=</span><span class="s1">&#39;result.jpg&#39;</span><span class="p">)</span>


<span class="n">asyncio</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">main</span><span class="p">())</span>

</pre></div>
</div>
</section>
<section id="demos">
<h3>Demos<a class="headerlink" href="#demos" title="Permalink to this heading">¶</a></h3>
<p>We also provide three demo scripts, implemented with high-level APIs and supporting functionality codes.
Source codes are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/demo">here</a>.</p>
<section id="image-demo">
<h4>Image demo<a class="headerlink" href="#image-demo" title="Permalink to this heading">¶</a></h4>
<p>This script performs inference on a single image.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/image_demo.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">IMAGE_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--device <span class="si">${</span><span class="nv">GPU_ID</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--score-thr <span class="si">${</span><span class="nv">SCORE_THR</span><span class="si">}</span><span class="o">]</span>
</pre></div>
</div>
<p>Examples:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/image_demo.py demo/demo.jpg <span class="se">\</span>
    configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth <span class="se">\</span>
    --device cpu
</pre></div>
</div>
</section>
<section id="webcam-demo">
<h4>Webcam demo<a class="headerlink" href="#webcam-demo" title="Permalink to this heading">¶</a></h4>
<p>This is a live demo from a webcam.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/webcam_demo.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--device <span class="si">${</span><span class="nv">GPU_ID</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--camera-id <span class="si">${</span><span class="nv">CAMERA</span><span class="p">-ID</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--score-thr <span class="si">${</span><span class="nv">SCORE_THR</span><span class="si">}</span><span class="o">]</span>
</pre></div>
</div>
<p>Examples:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/webcam_demo.py <span class="se">\</span>
    configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth
</pre></div>
</div>
</section>
<section id="video-demo">
<h4>Video demo<a class="headerlink" href="#video-demo" title="Permalink to this heading">¶</a></h4>
<p>This script performs inference on a video.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/video_demo.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">VIDEO_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--device <span class="si">${</span><span class="nv">GPU_ID</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--score-thr <span class="si">${</span><span class="nv">SCORE_THR</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--out <span class="si">${</span><span class="nv">OUT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--show<span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--wait-time <span class="si">${</span><span class="nv">WAIT_TIME</span><span class="si">}</span><span class="o">]</span>
</pre></div>
</div>
<p>Examples:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/video_demo.py demo/demo.mp4 <span class="se">\</span>
    configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth <span class="se">\</span>
    --out result.mp4
</pre></div>
</div>
</section>
<section id="video-demo-with-gpu-acceleration">
<h4>Video demo with GPU acceleration<a class="headerlink" href="#video-demo-with-gpu-acceleration" title="Permalink to this heading">¶</a></h4>
<p>This script performs inference on a video with GPU acceleration.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/video_gpuaccel_demo.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">VIDEO_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--device <span class="si">${</span><span class="nv">GPU_ID</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--score-thr <span class="si">${</span><span class="nv">SCORE_THR</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--nvdecode<span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--out <span class="si">${</span><span class="nv">OUT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--show<span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--wait-time <span class="si">${</span><span class="nv">WAIT_TIME</span><span class="si">}</span><span class="o">]</span>
</pre></div>
</div>
<p>Examples:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python demo/video_gpuaccel_demo.py demo/demo.mp4 <span class="se">\</span>
    configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth <span class="se">\</span>
    --nvdecode --out result.mp4
</pre></div>
</div>
</section>
</section>
</section>
<section id="test-existing-models-on-standard-datasets">
<h2>Test existing models on standard datasets<a class="headerlink" href="#test-existing-models-on-standard-datasets" title="Permalink to this heading">¶</a></h2>
<p>To evaluate a model’s accuracy, one usually tests the model on some standard datasets.
MMDetection supports multiple public datasets including COCO, Pascal VOC, CityScapes, and <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/_base_/datasets">more</a>.
This section will show how to test existing models on supported datasets.</p>
<section id="prepare-datasets">
<h3>Prepare datasets<a class="headerlink" href="#prepare-datasets" title="Permalink to this heading">¶</a></h3>
<p>Public datasets like <a class="reference external" href="http://host.robots.ox.ac.uk/pascal/VOC/index.html">Pascal VOC</a> or mirror and <a class="reference external" href="https://cocodataset.org/#download">COCO</a> are available from official websites or mirrors. Note: In the detection task, Pascal VOC 2012 is an extension of Pascal VOC 2007 without overlap, and we usually use them together.
It is recommended to download and extract the dataset somewhere outside the project directory and symlink the dataset root to <code class="docutils literal notranslate"><span class="pre">$MMDETECTION/data</span></code> as below.
If your folder structure is different, you may need to change the corresponding paths in config files.</p>
<p>We provide a script to download datasets such as COCO , you can run <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">tools/misc/download_dataset.py</span> <span class="pre">--dataset-name</span> <span class="pre">coco2017</span></code> to download COCO dataset.</p>
<p>For more usage please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/docs/en/useful_tools.md#dataset-download">dataset-download</a></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>rsidetection
├── rsidet
├── tools
├── configs
├── data
│   ├── coco
│   │   ├── annotations
│   │   ├── train2017
│   │   ├── val2017
│   │   ├── test2017
│   ├── cityscapes
│   │   ├── annotations
│   │   ├── leftImg8bit
│   │   │   ├── train
│   │   │   ├── val
│   │   ├── gtFine
│   │   │   ├── train
│   │   │   ├── val
│   ├── VOCdevkit
│   │   ├── VOC2007
│   │   ├── VOC2012
</pre></div>
</div>
<p>Some models require additional <a class="reference external" href="http://calvin.inf.ed.ac.uk/wp-content/uploads/data/cocostuffdataset/stuffthingmaps_trainval2017.zip">COCO-stuff</a> datasets, such as HTC, DetectoRS and SCNet, you can download and unzip then move to the coco folder. The directory should be like this.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>rsidetection
├── data
│   ├── coco
│   │   ├── annotations
│   │   ├── train2017
│   │   ├── val2017
│   │   ├── test2017
│   │   ├── stuffthingmaps
</pre></div>
</div>
<p>Panoptic segmentation models like PanopticFPN require additional <a class="reference external" href="http://images.cocodataset.org/annotations/panoptic_annotations_trainval2017.zip">COCO Panoptic</a> datasets, you can download and unzip then move to the coco annotation folder. The directory should be like this.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>rsidetection
├── data
│   ├── coco
│   │   ├── annotations
│   │   │   ├── panoptic_train2017.json
│   │   │   ├── panoptic_train2017
│   │   │   ├── panoptic_val2017.json
│   │   │   ├── panoptic_val2017
│   │   ├── train2017
│   │   ├── val2017
│   │   ├── test2017
</pre></div>
</div>
<p>The <a class="reference external" href="https://www.cityscapes-dataset.com/">cityscapes</a> annotations need to be converted into the coco format using <code class="docutils literal notranslate"><span class="pre">tools/dataset_converters/cityscapes.py</span></code>:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>pip install cityscapesscripts

python tools/dataset_converters/cityscapes.py <span class="se">\</span>
    ./data/cityscapes <span class="se">\</span>
    --nproc <span class="m">8</span> <span class="se">\</span>
    --out-dir ./data/cityscapes/annotations
</pre></div>
</div>
<p>TODO: CHANGE TO THE NEW PATH</p>
</section>
<section id="test-existing-models">
<h3>Test existing models<a class="headerlink" href="#test-existing-models" title="Permalink to this heading">¶</a></h3>
<p>We provide testing scripts for evaluating an existing model on the whole dataset (COCO, PASCAL VOC, Cityscapes, etc.).
The following testing environments are supported:</p>
<ul class="simple">
<li><p>single GPU</p></li>
<li><p>CPU</p></li>
<li><p>single node multiple GPUs</p></li>
<li><p>multiple nodes</p></li>
</ul>
<p>Choose the proper script to perform testing depending on the testing environment.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># single-gpu testing</span>
python tools/test.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--show<span class="o">]</span>

<span class="c1"># CPU: disable GPUs and run single-gpu testing script</span>
<span class="nb">export</span> <span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span>-1
python tools/test.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--show<span class="o">]</span>

<span class="c1"># multi-gpu testing</span>
bash tools/dist_test.sh <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">GPU_NUM</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">tools/dist_test.sh</span></code> also supports multi-node testing, but relies on PyTorch’s <a class="reference external" href="https://pytorch.org/docs/stable/distributed.html#launch-utility">launch utility</a>.</p>
<p>Optional arguments:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">RESULT_FILE</span></code>: Filename of the output results in pickle format. If not specified, the results will not be saved to a file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">EVAL_METRICS</span></code>: Items to be evaluated on the results. Allowed values depend on the dataset, e.g., <code class="docutils literal notranslate"><span class="pre">proposal_fast</span></code>, <code class="docutils literal notranslate"><span class="pre">proposal</span></code>, <code class="docutils literal notranslate"><span class="pre">bbox</span></code>, <code class="docutils literal notranslate"><span class="pre">segm</span></code> are available for COCO, <code class="docutils literal notranslate"><span class="pre">mAP</span></code>, <code class="docutils literal notranslate"><span class="pre">recall</span></code> for PASCAL VOC. Cityscapes could be evaluated by <code class="docutils literal notranslate"><span class="pre">cityscapes</span></code> as well as all COCO metrics.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--show</span></code>: If specified, detection results will be plotted on the images and shown in a new window. It is only applicable to single GPU testing and used for debugging and visualization. Please make sure that GUI is available in your environment. Otherwise, you may encounter an error like <code class="docutils literal notranslate"><span class="pre">cannot</span> <span class="pre">connect</span> <span class="pre">to</span> <span class="pre">X</span> <span class="pre">server</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--show-dir</span></code>: If specified, detection results will be plotted on the images and saved to the specified directory. It is only applicable to single GPU testing and used for debugging and visualization. You do NOT need a GUI available in your environment for using this option.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--show-score-thr</span></code>: If specified, detections with scores below this threshold will be removed.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--cfg-options</span></code>:  if specified, the key-value pair optional cfg will be merged into config file</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--eval-options</span></code>: if specified, the key-value pair optional eval cfg will be kwargs for dataset.evaluate() function, it’s only for evaluation</p></li>
</ul>
</section>
<section id="examples">
<h3>Examples<a class="headerlink" href="#examples" title="Permalink to this heading">¶</a></h3>
<p>Assuming that you have already downloaded the checkpoints to the directory <code class="docutils literal notranslate"><span class="pre">checkpoints/</span></code>.</p>
<ol class="arabic">
<li><p>Test Faster R-CNN and visualize the results. Press any key for the next image.
Config and checkpoint files are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/faster_rcnn">here</a>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/test.py <span class="se">\</span>
    configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth <span class="se">\</span>
    --show
</pre></div>
</div>
</li>
<li><p>Test Faster R-CNN and save the painted images for future visualization.
Config and checkpoint files are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/faster_rcnn">here</a>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/test.py <span class="se">\</span>
    configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth <span class="se">\</span>
    --show-dir faster_rcnn_r50_fpn_1x_results
</pre></div>
</div>
</li>
<li><p>Test Faster R-CNN on PASCAL VOC (without saving the test results) and evaluate the mAP.
Config and checkpoint files are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/pascal_voc">here</a>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/test.py <span class="se">\</span>
    configs/pascal_voc/faster_rcnn_r50_fpn_1x_voc.py <span class="se">\</span>
    checkpoints/faster_rcnn_r50_fpn_1x_voc0712_20200624-c9895d40.pth <span class="se">\</span>
    --eval mAP
</pre></div>
</div>
</li>
<li><p>Test Mask R-CNN with 8 GPUs, and evaluate the bbox and mask AP.
Config and checkpoint files are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/mask_rcnn">here</a>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>./tools/dist_test.sh <span class="se">\</span>
    configs/mask_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/mask_rcnn_r50_fpn_1x_coco_20200205-d4b0c5d6.pth <span class="se">\</span>
    <span class="m">8</span> <span class="se">\</span>
    --out results.pkl <span class="se">\</span>
    --eval bbox segm
</pre></div>
</div>
</li>
<li><p>Test Mask R-CNN with 8 GPUs, and evaluate the <strong>classwise</strong> bbox and mask AP.
Config and checkpoint files are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/mask_rcnn">here</a>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>./tools/dist_test.sh <span class="se">\</span>
    configs/mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/mask_rcnn_r50_fpn_1x_coco_20200205-d4b0c5d6.pth <span class="se">\</span>
    <span class="m">8</span> <span class="se">\</span>
    --out results.pkl <span class="se">\</span>
    --eval bbox segm <span class="se">\</span>
    --options <span class="s2">&quot;classwise=True&quot;</span>
</pre></div>
</div>
</li>
<li><p>Test Mask R-CNN on COCO test-dev with 8 GPUs, and generate JSON files for submitting to the official evaluation server.
Config and checkpoint files are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/mask_rcnn">here</a>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>./tools/dist_test.sh <span class="se">\</span>
    configs/mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/mask_rcnn_r50_fpn_1x_coco_20200205-d4b0c5d6.pth <span class="se">\</span>
    <span class="m">8</span> <span class="se">\</span>
    --format-only <span class="se">\</span>
    --options <span class="s2">&quot;jsonfile_prefix=./mask_rcnn_test-dev_results&quot;</span>
</pre></div>
</div>
<p>This command generates two JSON files <code class="docutils literal notranslate"><span class="pre">mask_rcnn_test-dev_results.bbox.json</span></code> and <code class="docutils literal notranslate"><span class="pre">mask_rcnn_test-dev_results.segm.json</span></code>.</p>
</li>
<li><p>Test Mask R-CNN on Cityscapes test with 8 GPUs, and generate txt and png files for submitting to the official evaluation server.
Config and checkpoint files are available <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/cityscapes">here</a>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>./tools/dist_test.sh <span class="se">\</span>
    configs/cityscapes/mask_rcnn_r50_fpn_1x_cityscapes.py <span class="se">\</span>
    checkpoints/mask_rcnn_r50_fpn_1x_cityscapes_20200227-afe51d5a.pth <span class="se">\</span>
    <span class="m">8</span> <span class="se">\</span>
    --format-only <span class="se">\</span>
    --options <span class="s2">&quot;txtfile_prefix=./mask_rcnn_cityscapes_test_results&quot;</span>
</pre></div>
</div>
<p>The generated png and txt would be under <code class="docutils literal notranslate"><span class="pre">./mask_rcnn_cityscapes_test_results</span></code> directory.</p>
</li>
</ol>
</section>
<section id="test-without-ground-truth-annotations">
<h3>Test without Ground Truth Annotations<a class="headerlink" href="#test-without-ground-truth-annotations" title="Permalink to this heading">¶</a></h3>
<p>MMDetection supports to test models without ground-truth annotations using <code class="docutils literal notranslate"><span class="pre">CocoDataset</span></code>. If your dataset format is not in COCO format, please convert them to COCO format. For example, if your dataset format is VOC, you can directly convert it to COCO format by the <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/tools/dataset_converters/pascal_voc.py">script in tools.</a> If your dataset format is Cityscapes, you can directly convert it to COCO format by the <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/tools/dataset_converters/cityscapes.py">script in tools.</a> The rest of the formats can be converted using <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/tools/dataset_converters/images2coco.py">this script</a>.</p>
<div class="highlight-shel notranslate"><div class="highlight"><pre><span></span>python tools/dataset_converters/images2coco.py \
    ${IMG_PATH} \
    ${CLASSES} \
    ${OUT} \
    [--exclude-extensions]
</pre></div>
</div>
<p>arguments：</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">IMG_PATH</span></code>: The root path of images.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">CLASSES</span></code>: The text file with a list of categories.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">OUT</span></code>: The output annotation json file name. The save dir is in the same directory as <code class="docutils literal notranslate"><span class="pre">IMG_PATH</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">exclude-extensions</span></code>: The suffix of images to be excluded, such as ‘png’ and ‘bmp’.</p></li>
</ul>
<p>After the conversion is complete, you can use the following command to test</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># single-gpu testing</span>
python tools/test.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    --format-only <span class="se">\</span>
    --options <span class="si">${</span><span class="nv">JSONFILE_PREFIX</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--show<span class="o">]</span>

<span class="c1"># CPU: disable GPUs and run single-gpu testing script</span>
<span class="nb">export</span> <span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span>-1
python tools/test.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> <span class="se">\</span>
    <span class="o">[</span>--show<span class="o">]</span>

<span class="c1"># multi-gpu testing</span>
bash tools/dist_test.sh <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">GPU_NUM</span><span class="si">}</span> <span class="se">\</span>
    --format-only <span class="se">\</span>
    --options <span class="si">${</span><span class="nv">JSONFILE_PREFIX</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>--show<span class="o">]</span>
</pre></div>
</div>
<p>Assuming that the checkpoints in the <a class="reference external" href="https://rsidetection.readthedocs.io/en/latest/modelzoo_statistics.html">model zoo</a> have been downloaded to the directory <code class="docutils literal notranslate"><span class="pre">checkpoints/</span></code>, we can test Mask R-CNN on COCO test-dev with 8 GPUs, and generate JSON files using the following command.</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span>./tools/dist_test.sh <span class="se">\</span>
    configs/mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/mask_rcnn_r50_fpn_1x_coco_20200205-d4b0c5d6.pth <span class="se">\</span>
    <span class="m">8</span> <span class="se">\</span>
    -format-only <span class="se">\</span>
    --options <span class="s2">&quot;jsonfile_prefix=./mask_rcnn_test-dev_results&quot;</span>
</pre></div>
</div>
<p>This command generates two JSON files <code class="docutils literal notranslate"><span class="pre">mask_rcnn_test-dev_results.bbox.json</span></code> and <code class="docutils literal notranslate"><span class="pre">mask_rcnn_test-dev_results.segm.json</span></code>.</p>
</section>
<section id="batch-inference">
<h3>Batch Inference<a class="headerlink" href="#batch-inference" title="Permalink to this heading">¶</a></h3>
<p>MMDetection supports inference with a single image or batched images in test mode. By default, we use single-image inference and you can use batch inference by modifying <code class="docutils literal notranslate"><span class="pre">samples_per_gpu</span></code> in the config of test data. You can do that either by modifying the config as below.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nv">data</span> <span class="o">=</span> dict<span class="o">(</span><span class="nv">train</span><span class="o">=</span>dict<span class="o">(</span>...<span class="o">)</span>, <span class="nv">val</span><span class="o">=</span>dict<span class="o">(</span>...<span class="o">)</span>, <span class="nv">test</span><span class="o">=</span>dict<span class="o">(</span><span class="nv">samples_per_gpu</span><span class="o">=</span><span class="m">2</span>, ...<span class="o">))</span>
</pre></div>
</div>
<p>Or you can set it through <code class="docutils literal notranslate"><span class="pre">--cfg-options</span></code> as <code class="docutils literal notranslate"><span class="pre">--cfg-options</span> <span class="pre">data.test.samples_per_gpu=2</span></code></p>
</section>
<section id="deprecated-imagetotensor">
<h3>Deprecated ImageToTensor<a class="headerlink" href="#deprecated-imagetotensor" title="Permalink to this heading">¶</a></h3>
<p>In test mode,  <code class="docutils literal notranslate"><span class="pre">ImageToTensor</span></code>  pipeline is deprecated, it’s replaced by <code class="docutils literal notranslate"><span class="pre">DefaultFormatBundle</span></code> that recommended to manually replace it in the test data pipeline in your config file.  examples:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># use ImageToTensor (deprecated)</span>
<span class="n">pipelines</span> <span class="o">=</span> <span class="p">[</span>
   <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;LoadImageFromFile&#39;</span><span class="p">),</span>
   <span class="nb">dict</span><span class="p">(</span>
       <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MultiScaleFlipAug&#39;</span><span class="p">,</span>
       <span class="n">img_scale</span><span class="o">=</span><span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">800</span><span class="p">),</span>
       <span class="n">flip</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span>
       <span class="n">transforms</span><span class="o">=</span><span class="p">[</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Resize&#39;</span><span class="p">,</span> <span class="n">keep_ratio</span><span class="o">=</span><span class="bp">True</span><span class="p">),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RandomFlip&#39;</span><span class="p">),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Normalize&#39;</span><span class="p">,</span> <span class="n">mean</span><span class="o">=</span><span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">],</span> <span class="n">std</span><span class="o">=</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">]),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Pad&#39;</span><span class="p">,</span> <span class="n">size_divisor</span><span class="o">=</span><span class="mi">32</span><span class="p">),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ImageToTensor&#39;</span><span class="p">,</span> <span class="n">keys</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;img&#39;</span><span class="p">]),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Collect&#39;</span><span class="p">,</span> <span class="n">keys</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;img&#39;</span><span class="p">]),</span>
       <span class="p">])</span>
   <span class="p">]</span>

<span class="c1"># manually replace ImageToTensor to DefaultFormatBundle (recommended)</span>
<span class="n">pipelines</span> <span class="o">=</span> <span class="p">[</span>
   <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;LoadImageFromFile&#39;</span><span class="p">),</span>
   <span class="nb">dict</span><span class="p">(</span>
       <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MultiScaleFlipAug&#39;</span><span class="p">,</span>
       <span class="n">img_scale</span><span class="o">=</span><span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">800</span><span class="p">),</span>
       <span class="n">flip</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span>
       <span class="n">transforms</span><span class="o">=</span><span class="p">[</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Resize&#39;</span><span class="p">,</span> <span class="n">keep_ratio</span><span class="o">=</span><span class="bp">True</span><span class="p">),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RandomFlip&#39;</span><span class="p">),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Normalize&#39;</span><span class="p">,</span> <span class="n">mean</span><span class="o">=</span><span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">],</span> <span class="n">std</span><span class="o">=</span><span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">]),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Pad&#39;</span><span class="p">,</span> <span class="n">size_divisor</span><span class="o">=</span><span class="mi">32</span><span class="p">),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;DefaultFormatBundle&#39;</span><span class="p">),</span>
           <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Collect&#39;</span><span class="p">,</span> <span class="n">keys</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;img&#39;</span><span class="p">]),</span>
       <span class="p">])</span>
   <span class="p">]</span>
</pre></div>
</div>
</section>
</section>
<section id="train-predefined-models-on-standard-datasets">
<h2>Train predefined models on standard datasets<a class="headerlink" href="#train-predefined-models-on-standard-datasets" title="Permalink to this heading">¶</a></h2>
<p>MMDetection also provides out-of-the-box tools for training detection models.
This section will show how to train <em>predefined</em> models (under <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs">configs</a>) on standard datasets i.e. COCO.</p>
<section id="id1">
<h3>Prepare datasets<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h3>
<p>Training requires preparing datasets too. See section <a class="reference internal" href="#prepare-datasets"><span class="std std-doc">Prepare datasets</span></a> above for details.</p>
<p><strong>Note</strong>:
Currently, the config files under <code class="docutils literal notranslate"><span class="pre">configs/cityscapes</span></code> use COCO pretrained weights to initialize.
You could download the existing models in advance if the network connection is unavailable or slow. Otherwise, it would cause errors at the beginning of training.</p>
</section>
<section id="learning-rate-automatically-scale">
<h3>Learning rate automatically scale<a class="headerlink" href="#learning-rate-automatically-scale" title="Permalink to this heading">¶</a></h3>
<p><strong>Important</strong>: The default learning rate in config files is for 8 GPUs and 2 sample per gpu (batch size = 8 * 2 = 16). And it had been set to <code class="docutils literal notranslate"><span class="pre">auto_scale_lr.base_batch_size</span></code> in <code class="docutils literal notranslate"><span class="pre">config/_base_/default_runtime.py</span></code>. Learning rate will be automatically scaled base on this value when the batch size is <code class="docutils literal notranslate"><span class="pre">16</span></code>. Meanwhile, in order not to affect other codebase which based on rsidet, the flag <code class="docutils literal notranslate"><span class="pre">auto_scale_lr.enable</span></code> is set to <code class="docutils literal notranslate"><span class="pre">False</span></code> by default.</p>
<p>If you want to enable this feature, you need to add argument <code class="docutils literal notranslate"><span class="pre">--auto-scale-lr</span></code>. And you need to check the config name which you want to use before you process the command, because the config name indicates the default batch size.
By default, it is <code class="docutils literal notranslate"><span class="pre">8</span> <span class="pre">x</span> <span class="pre">2</span> <span class="pre">=</span> <span class="pre">16</span> <span class="pre">batch</span> <span class="pre">size</span></code>, like <code class="docutils literal notranslate"><span class="pre">faster_rcnn_r50_caffe_fpn_90k_coco.py</span></code> or <code class="docutils literal notranslate"><span class="pre">pisa_faster_rcnn_x101_32x4d_fpn_1x_coco.py</span></code>. In other cases, you will see the config file name have <code class="docutils literal notranslate"><span class="pre">_NxM_</span></code> in dictating, like <code class="docutils literal notranslate"><span class="pre">cornernet_hourglass104_mstest_32x3_210e_coco.py</span></code> which batch size is <code class="docutils literal notranslate"><span class="pre">32</span> <span class="pre">x</span> <span class="pre">3</span> <span class="pre">=</span> <span class="pre">96</span></code>, or <code class="docutils literal notranslate"><span class="pre">scnet_x101_64x4d_fpn_8x1_20e_coco.py</span></code> which batch size is <code class="docutils literal notranslate"><span class="pre">8</span> <span class="pre">x</span> <span class="pre">1</span> <span class="pre">=</span> <span class="pre">8</span></code>.</p>
<p><strong>Please remember to check the bottom of the specific config file you want to use, it will have <code class="docutils literal notranslate"><span class="pre">auto_scale_lr.base_batch_size</span></code> if the batch size is not <code class="docutils literal notranslate"><span class="pre">16</span></code>. If you can’t find those values, check the config file which in <code class="docutils literal notranslate"><span class="pre">_base_=[xxx]</span></code> and you will find it. Please do not modify its values if you want to automatically scale the LR.</strong></p>
<p>Learning rate automatically scale basic usage is as follows.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/train.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    --auto-scale-lr <span class="se">\</span>
    <span class="o">[</span>optional arguments<span class="o">]</span>
</pre></div>
</div>
<p>If you enabled this feature, the learning rate will be automatically scaled according to the number of GPUs of the machine and the batch size of training. See <a class="reference external" href="https://arxiv.org/abs/1706.02677">linear scaling rule</a> for details. For example, If there are 4 GPUs and 2 pictures on each GPU, <code class="docutils literal notranslate"><span class="pre">lr</span> <span class="pre">=</span> <span class="pre">0.01</span></code>, then if there are 16 GPUs and 4 pictures on each GPU, it will automatically scale to <code class="docutils literal notranslate"><span class="pre">lr</span> <span class="pre">=</span> <span class="pre">0.08</span></code>.</p>
<p>If you don’t want to use it, you need to calculate the learning rate according to the <a class="reference external" href="https://arxiv.org/abs/1706.02677">linear scaling rule</a> manually then change <code class="docutils literal notranslate"><span class="pre">optimizer.lr</span></code> in specific config file.</p>
</section>
<section id="training-on-a-single-gpu">
<h3>Training on a single GPU<a class="headerlink" href="#training-on-a-single-gpu" title="Permalink to this heading">¶</a></h3>
<p>We provide <code class="docutils literal notranslate"><span class="pre">tools/train.py</span></code> to launch training jobs on a single GPU.
The basic usage is as follows.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/train.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>optional arguments<span class="o">]</span>
</pre></div>
</div>
<p>During training, log files and checkpoints will be saved to the working directory, which is specified by <code class="docutils literal notranslate"><span class="pre">work_dir</span></code> in the config file or via CLI argument <code class="docutils literal notranslate"><span class="pre">--work-dir</span></code>.</p>
<p>By default, the model is evaluated on the validation set every epoch, the evaluation interval can be specified in the config file as shown below.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># evaluate the model every 12 epoch.</span>
<span class="n">evaluation</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">interval</span><span class="o">=</span><span class="mi">12</span><span class="p">)</span>
</pre></div>
</div>
<p>This tool accepts several optional arguments, including:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">--no-validate</span></code> (<strong>not suggested</strong>): Disable evaluation during training.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--work-dir</span> <span class="pre">${WORK_DIR}</span></code>: Override the working directory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--resume-from</span> <span class="pre">${CHECKPOINT_FILE}</span></code>: Resume from a previous checkpoint file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--options</span> <span class="pre">'Key=value'</span></code>: Overrides other settings in the used config.</p></li>
</ul>
<p><strong>Note</strong>:</p>
<p>Difference between <code class="docutils literal notranslate"><span class="pre">resume-from</span></code> and <code class="docutils literal notranslate"><span class="pre">load-from</span></code>:</p>
<p><code class="docutils literal notranslate"><span class="pre">resume-from</span></code> loads both the model weights and optimizer status, and the epoch is also inherited from the specified checkpoint. It is usually used for resuming the training process that is interrupted accidentally.
<code class="docutils literal notranslate"><span class="pre">load-from</span></code> only loads the model weights and the training epoch starts from 0. It is usually used for finetuning.</p>
</section>
<section id="training-on-cpu">
<h3>Training on CPU<a class="headerlink" href="#training-on-cpu" title="Permalink to this heading">¶</a></h3>
<p>The process of training on the CPU is consistent with single GPU training. We just need to disable GPUs before the training process.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nb">export</span> <span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span>-1
</pre></div>
</div>
<p>And then run the script <span class="xref myst">above</span>.</p>
<p><strong>Note</strong>:</p>
<p>We do not recommend users to use CPU for training because it is too slow. We support this feature to allow users to debug on machines without GPU for convenience.</p>
</section>
<section id="training-on-multiple-gpus">
<h3>Training on multiple GPUs<a class="headerlink" href="#training-on-multiple-gpus" title="Permalink to this heading">¶</a></h3>
<p>We provide <code class="docutils literal notranslate"><span class="pre">tools/dist_train.sh</span></code> to launch training on multiple GPUs.
The basic usage is as follows.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>bash ./tools/dist_train.sh <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">GPU_NUM</span><span class="si">}</span> <span class="se">\</span>
    <span class="o">[</span>optional arguments<span class="o">]</span>
</pre></div>
</div>
<p>Optional arguments remain the same as stated <span class="xref myst">above</span>.</p>
<section id="launch-multiple-jobs-simultaneously">
<h4>Launch multiple jobs simultaneously<a class="headerlink" href="#launch-multiple-jobs-simultaneously" title="Permalink to this heading">¶</a></h4>
<p>If you would like to launch multiple jobs on a single machine, e.g., 2 jobs of 4-GPU training on a machine with 8 GPUs,
you need to specify different ports (29500 by default) for each job to avoid communication conflict.</p>
<p>If you use <code class="docutils literal notranslate"><span class="pre">dist_train.sh</span></code> to launch training jobs, you can set the port in commands.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">0</span>,1,2,3 <span class="nv">PORT</span><span class="o">=</span><span class="m">29500</span> ./tools/dist_train.sh <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="m">4</span>
<span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">4</span>,5,6,7 <span class="nv">PORT</span><span class="o">=</span><span class="m">29501</span> ./tools/dist_train.sh <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="m">4</span>
</pre></div>
</div>
</section>
</section>
<section id="train-with-multiple-machines">
<h3>Train with multiple machines<a class="headerlink" href="#train-with-multiple-machines" title="Permalink to this heading">¶</a></h3>
<p>If you launch with multiple machines simply connected with ethernet, you can simply run following commands:</p>
<p>On the first machine:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nv">NNODES</span><span class="o">=</span><span class="m">2</span> <span class="nv">NODE_RANK</span><span class="o">=</span><span class="m">0</span> <span class="nv">PORT</span><span class="o">=</span><span class="nv">$MASTER_PORT</span> <span class="nv">MASTER_ADDR</span><span class="o">=</span><span class="nv">$MASTER_ADDR</span> sh tools/dist_train.sh <span class="nv">$CONFIG</span> <span class="nv">$GPUS</span>
</pre></div>
</div>
<p>On the second machine:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nv">NNODES</span><span class="o">=</span><span class="m">2</span> <span class="nv">NODE_RANK</span><span class="o">=</span><span class="m">1</span> <span class="nv">PORT</span><span class="o">=</span><span class="nv">$MASTER_PORT</span> <span class="nv">MASTER_ADDR</span><span class="o">=</span><span class="nv">$MASTER_ADDR</span> sh tools/dist_train.sh <span class="nv">$CONFIG</span> <span class="nv">$GPUS</span>
</pre></div>
</div>
<p>Usually it is slow if you do not have high speed networking like InfiniBand.</p>
</section>
<section id="manage-jobs-with-slurm">
<h3>Manage jobs with Slurm<a class="headerlink" href="#manage-jobs-with-slurm" title="Permalink to this heading">¶</a></h3>
<p><a class="reference external" href="https://slurm.schedmd.com/">Slurm</a> is a good job scheduling system for computing clusters.
On a cluster managed by Slurm, you can use <code class="docutils literal notranslate"><span class="pre">slurm_train.sh</span></code> to spawn training jobs. It supports both single-node and multi-node training.</p>
<p>The basic usage is as follows.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="o">[</span><span class="nv">GPUS</span><span class="o">=</span><span class="si">${</span><span class="nv">GPUS</span><span class="si">}</span><span class="o">]</span> ./tools/slurm_train.sh <span class="si">${</span><span class="nv">PARTITION</span><span class="si">}</span> <span class="si">${</span><span class="nv">JOB_NAME</span><span class="si">}</span> <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span>
</pre></div>
</div>
<p>Below is an example of using 16 GPUs to train Mask R-CNN on a Slurm partition named <em>dev</em>, and set the work-dir to some shared file systems.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nv">GPUS</span><span class="o">=</span><span class="m">16</span> ./tools/slurm_train.sh dev mask_r50_1x configs/mask_rcnn_r50_fpn_1x_coco.py /nfs/xxxx/mask_rcnn_r50_fpn_1x
</pre></div>
</div>
<p>You can check <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/slurm_train.sh">the source code</a> to review full arguments and environment variables.</p>
<p>When using Slurm, the port option need to be set in one of the following ways:</p>
<ol class="arabic">
<li><p>Set the port through <code class="docutils literal notranslate"><span class="pre">--options</span></code>. This is more recommended since it does not change the original configs.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">0</span>,1,2,3 <span class="nv">GPUS</span><span class="o">=</span><span class="m">4</span> ./tools/slurm_train.sh <span class="si">${</span><span class="nv">PARTITION</span><span class="si">}</span> <span class="si">${</span><span class="nv">JOB_NAME</span><span class="si">}</span> config1.py <span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span> --options <span class="s1">&#39;dist_params.port=29500&#39;</span>
<span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">4</span>,5,6,7 <span class="nv">GPUS</span><span class="o">=</span><span class="m">4</span> ./tools/slurm_train.sh <span class="si">${</span><span class="nv">PARTITION</span><span class="si">}</span> <span class="si">${</span><span class="nv">JOB_NAME</span><span class="si">}</span> config2.py <span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span> --options <span class="s1">&#39;dist_params.port=29501&#39;</span>
</pre></div>
</div>
</li>
<li><p>Modify the config files to set different communication ports.</p>
<p>In <code class="docutils literal notranslate"><span class="pre">config1.py</span></code>, set</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dist_params</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">backend</span><span class="o">=</span><span class="s1">&#39;nccl&#39;</span><span class="p">,</span> <span class="n">port</span><span class="o">=</span><span class="mi">29500</span><span class="p">)</span>
</pre></div>
</div>
<p>In <code class="docutils literal notranslate"><span class="pre">config2.py</span></code>, set</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dist_params</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">backend</span><span class="o">=</span><span class="s1">&#39;nccl&#39;</span><span class="p">,</span> <span class="n">port</span><span class="o">=</span><span class="mi">29501</span><span class="p">)</span>
</pre></div>
</div>
<p>Then you can launch two jobs with <code class="docutils literal notranslate"><span class="pre">config1.py</span></code> and <code class="docutils literal notranslate"><span class="pre">config2.py</span></code>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">0</span>,1,2,3 <span class="nv">GPUS</span><span class="o">=</span><span class="m">4</span> ./tools/slurm_train.sh <span class="si">${</span><span class="nv">PARTITION</span><span class="si">}</span> <span class="si">${</span><span class="nv">JOB_NAME</span><span class="si">}</span> config1.py <span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span>
<span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">4</span>,5,6,7 <span class="nv">GPUS</span><span class="o">=</span><span class="m">4</span> ./tools/slurm_train.sh <span class="si">${</span><span class="nv">PARTITION</span><span class="si">}</span> <span class="si">${</span><span class="nv">JOB_NAME</span><span class="si">}</span> config2.py <span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span>
</pre></div>
</div>
</li>
</ol>
</section>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="2_new_data_model.html" class="btn btn-neutral float-right" title="2: Train with customized datasets" accesskey="n"
      rel="next">Next <img src="_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="model_zoo.html" class="btn btn-neutral" title="Benchmark and Model Zoo" accesskey="p"
      rel="prev"><img src="_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">1: Inference and train with existing models and standard datasets</a><ul>
<li><a class="reference internal" href="#inference-with-existing-models">Inference with existing models</a><ul>
<li><a class="reference internal" href="#high-level-apis-for-inference">High-level APIs for inference</a></li>
<li><a class="reference internal" href="#asynchronous-interface-supported-for-python-3-7">Asynchronous interface - supported for Python 3.7+</a></li>
<li><a class="reference internal" href="#demos">Demos</a><ul>
<li><a class="reference internal" href="#image-demo">Image demo</a></li>
<li><a class="reference internal" href="#webcam-demo">Webcam demo</a></li>
<li><a class="reference internal" href="#video-demo">Video demo</a></li>
<li><a class="reference internal" href="#video-demo-with-gpu-acceleration">Video demo with GPU acceleration</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#test-existing-models-on-standard-datasets">Test existing models on standard datasets</a><ul>
<li><a class="reference internal" href="#prepare-datasets">Prepare datasets</a></li>
<li><a class="reference internal" href="#test-existing-models">Test existing models</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#test-without-ground-truth-annotations">Test without Ground Truth Annotations</a></li>
<li><a class="reference internal" href="#batch-inference">Batch Inference</a></li>
<li><a class="reference internal" href="#deprecated-imagetotensor">Deprecated ImageToTensor</a></li>
</ul>
</li>
<li><a class="reference internal" href="#train-predefined-models-on-standard-datasets">Train predefined models on standard datasets</a><ul>
<li><a class="reference internal" href="#id1">Prepare datasets</a></li>
<li><a class="reference internal" href="#learning-rate-automatically-scale">Learning rate automatically scale</a></li>
<li><a class="reference internal" href="#training-on-a-single-gpu">Training on a single GPU</a></li>
<li><a class="reference internal" href="#training-on-cpu">Training on CPU</a></li>
<li><a class="reference internal" href="#training-on-multiple-gpus">Training on multiple GPUs</a><ul>
<li><a class="reference internal" href="#launch-multiple-jobs-simultaneously">Launch multiple jobs simultaneously</a></li>
</ul>
</li>
<li><a class="reference internal" href="#train-with-multiple-machines">Train with multiple machines</a></li>
<li><a class="reference internal" href="#manage-jobs-with-slurm">Manage jobs with Slurm</a></li>
</ul>
</li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>