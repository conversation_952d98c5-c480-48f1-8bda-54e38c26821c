


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>2: Train with customized datasets &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="3: Train with customized models and standard datasets" href="3_exist_data_new_model.html" />
  <link rel="prev" title="1: Inference and train with existing models and standard datasets" href="1_exist_data_model.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>2: Train with customized datasets</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/2_new_data_model.md.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="train-with-customized-datasets">
<h1>2: Train with customized datasets<a class="headerlink" href="#train-with-customized-datasets" title="Permalink to this heading">¶</a></h1>
<p>In this note, you will know how to inference, test, and train predefined models with customized datasets. We use the <a class="reference external" href="https://github.com/matterport/Mask_RCNN/tree/master/samples/balloon">balloon dataset</a> as an example to describe the whole process.</p>
<p>The basic steps are as below:</p>
<ol class="arabic simple">
<li><p>Prepare the customized dataset</p></li>
<li><p>Prepare a config</p></li>
<li><p>Train, test, inference models on the customized dataset.</p></li>
</ol>
<section id="prepare-the-customized-dataset">
<h2>Prepare the customized dataset<a class="headerlink" href="#prepare-the-customized-dataset" title="Permalink to this heading">¶</a></h2>
<p>There are three ways to support a new dataset in MMDetection:</p>
<ol class="arabic simple">
<li><p>reorganize the dataset into COCO format.</p></li>
<li><p>reorganize the dataset into a middle format.</p></li>
<li><p>implement a new dataset.</p></li>
</ol>
<p>Usually we recommend to use the first two methods which are usually easier than the third.</p>
<p>In this note, we give an example for converting the data into COCO format.</p>
<p><strong>Note</strong>: MMDetection only supports evaluating mask AP of dataset in COCO format for now.
So for instance segmentation task users should convert the data into coco format.</p>
<section id="coco-annotation-format">
<h3>COCO annotation format<a class="headerlink" href="#coco-annotation-format" title="Permalink to this heading">¶</a></h3>
<p>The necessary keys of COCO format for instance segmentation is as below, for the complete details, please refer <a class="reference external" href="https://cocodataset.org/#format-data">here</a>.</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span>{
    &quot;images&quot;: [image],
    &quot;annotations&quot;: [annotation],
    &quot;categories&quot;: [category]
}


image = {
    &quot;id&quot;: int,
    &quot;width&quot;: int,
    &quot;height&quot;: int,
    &quot;file_name&quot;: str,
}

annotation = {
    &quot;id&quot;: int,
    &quot;image_id&quot;: int,
    &quot;category_id&quot;: int,
    &quot;segmentation&quot;: RLE or [polygon],
    &quot;area&quot;: float,
    &quot;bbox&quot;: [x,y,width,height],
    &quot;iscrowd&quot;: 0 or 1,
}

categories = [{
    &quot;id&quot;: int,
    &quot;name&quot;: str,
    &quot;supercategory&quot;: str,
}]
</pre></div>
</div>
<p>Assume we use the balloon dataset.
After downloading the data, we need to implement a function to convert the annotation format into the COCO format. Then we can use implemented COCODataset to load the data and perform training and evaluation.</p>
<p>If you take a look at the dataset, you will find the dataset format is as below:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span>{&#39;base64_img_data&#39;: &#39;&#39;,
 &#39;file_attributes&#39;: {},
 &#39;filename&#39;: &#39;34020010494_e5cb88e1c4_k.jpg&#39;,
 &#39;fileref&#39;: &#39;&#39;,
 &#39;regions&#39;: {&#39;0&#39;: {&#39;region_attributes&#39;: {},
   &#39;shape_attributes&#39;: {&#39;all_points_x&#39;: [1020,
     1000,
     994,
     1003,
     1023,
     1050,
     1089,
     1134,
     1190,
     1265,
     1321,
     1361,
     1403,
     1428,
     1442,
     1445,
     1441,
     1427,
     1400,
     1361,
     1316,
     1269,
     1228,
     1198,
     1207,
     1210,
     1190,
     1177,
     1172,
     1174,
     1170,
     1153,
     1127,
     1104,
     1061,
     1032,
     1020],
    &#39;all_points_y&#39;: [963,
     899,
     841,
     787,
     738,
     700,
     663,
     638,
     621,
     619,
     643,
     672,
     720,
     765,
     800,
     860,
     896,
     942,
     990,
     1035,
     1079,
     1112,
     1129,
     1134,
     1144,
     1153,
     1166,
     1166,
     1150,
     1136,
     1129,
     1122,
     1112,
     1084,
     1037,
     989,
     963],
    &#39;name&#39;: &#39;polygon&#39;}}},
 &#39;size&#39;: 1115004}
</pre></div>
</div>
<p>The annotation is a JSON file where each key indicates an image’s all annotations.
The code to convert the balloon dataset into coco format is as below.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os.path</span> <span class="kn">as</span> <span class="nn">osp</span>
<span class="kn">import</span> <span class="nn">mmcv</span>

<span class="k">def</span> <span class="nf">convert_balloon_to_coco</span><span class="p">(</span><span class="n">ann_file</span><span class="p">,</span> <span class="n">out_file</span><span class="p">,</span> <span class="n">image_prefix</span><span class="p">):</span>
    <span class="n">data_infos</span> <span class="o">=</span> <span class="n">mmcv</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">ann_file</span><span class="p">)</span>

    <span class="n">annotations</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="n">images</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="n">obj_count</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="k">for</span> <span class="n">idx</span><span class="p">,</span> <span class="n">v</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">mmcv</span><span class="o">.</span><span class="n">track_iter_progress</span><span class="p">(</span><span class="n">data_infos</span><span class="o">.</span><span class="n">values</span><span class="p">())):</span>
        <span class="n">filename</span> <span class="o">=</span> <span class="n">v</span><span class="p">[</span><span class="s1">&#39;filename&#39;</span><span class="p">]</span>
        <span class="n">img_path</span> <span class="o">=</span> <span class="n">osp</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">image_prefix</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
        <span class="n">height</span><span class="p">,</span> <span class="n">width</span> <span class="o">=</span> <span class="n">mmcv</span><span class="o">.</span><span class="n">imread</span><span class="p">(</span><span class="n">img_path</span><span class="p">)</span><span class="o">.</span><span class="n">shape</span><span class="p">[:</span><span class="mi">2</span><span class="p">]</span>

        <span class="n">images</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">dict</span><span class="p">(</span>
            <span class="nb">id</span><span class="o">=</span><span class="n">idx</span><span class="p">,</span>
            <span class="n">file_name</span><span class="o">=</span><span class="n">filename</span><span class="p">,</span>
            <span class="n">height</span><span class="o">=</span><span class="n">height</span><span class="p">,</span>
            <span class="n">width</span><span class="o">=</span><span class="n">width</span><span class="p">))</span>

        <span class="n">bboxes</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">labels</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">masks</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">_</span><span class="p">,</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">v</span><span class="p">[</span><span class="s1">&#39;regions&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="k">assert</span> <span class="ow">not</span> <span class="n">obj</span><span class="p">[</span><span class="s1">&#39;region_attributes&#39;</span><span class="p">]</span>
            <span class="n">obj</span> <span class="o">=</span> <span class="n">obj</span><span class="p">[</span><span class="s1">&#39;shape_attributes&#39;</span><span class="p">]</span>
            <span class="n">px</span> <span class="o">=</span> <span class="n">obj</span><span class="p">[</span><span class="s1">&#39;all_points_x&#39;</span><span class="p">]</span>
            <span class="n">py</span> <span class="o">=</span> <span class="n">obj</span><span class="p">[</span><span class="s1">&#39;all_points_y&#39;</span><span class="p">]</span>
            <span class="n">poly</span> <span class="o">=</span> <span class="p">[(</span><span class="n">x</span> <span class="o">+</span> <span class="mf">0.5</span><span class="p">,</span> <span class="n">y</span> <span class="o">+</span> <span class="mf">0.5</span><span class="p">)</span> <span class="k">for</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">px</span><span class="p">,</span> <span class="n">py</span><span class="p">)]</span>
            <span class="n">poly</span> <span class="o">=</span> <span class="p">[</span><span class="n">p</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">poly</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">x</span><span class="p">]</span>

            <span class="n">x_min</span><span class="p">,</span> <span class="n">y_min</span><span class="p">,</span> <span class="n">x_max</span><span class="p">,</span> <span class="n">y_max</span> <span class="o">=</span> <span class="p">(</span>
                <span class="nb">min</span><span class="p">(</span><span class="n">px</span><span class="p">),</span> <span class="nb">min</span><span class="p">(</span><span class="n">py</span><span class="p">),</span> <span class="nb">max</span><span class="p">(</span><span class="n">px</span><span class="p">),</span> <span class="nb">max</span><span class="p">(</span><span class="n">py</span><span class="p">))</span>


            <span class="n">data_anno</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
                <span class="n">image_id</span><span class="o">=</span><span class="n">idx</span><span class="p">,</span>
                <span class="nb">id</span><span class="o">=</span><span class="n">obj_count</span><span class="p">,</span>
                <span class="n">category_id</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">bbox</span><span class="o">=</span><span class="p">[</span><span class="n">x_min</span><span class="p">,</span> <span class="n">y_min</span><span class="p">,</span> <span class="n">x_max</span> <span class="o">-</span> <span class="n">x_min</span><span class="p">,</span> <span class="n">y_max</span> <span class="o">-</span> <span class="n">y_min</span><span class="p">],</span>
                <span class="n">area</span><span class="o">=</span><span class="p">(</span><span class="n">x_max</span> <span class="o">-</span> <span class="n">x_min</span><span class="p">)</span> <span class="o">*</span> <span class="p">(</span><span class="n">y_max</span> <span class="o">-</span> <span class="n">y_min</span><span class="p">),</span>
                <span class="n">segmentation</span><span class="o">=</span><span class="p">[</span><span class="n">poly</span><span class="p">],</span>
                <span class="n">iscrowd</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="n">annotations</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">data_anno</span><span class="p">)</span>
            <span class="n">obj_count</span> <span class="o">+=</span> <span class="mi">1</span>

    <span class="n">coco_format_json</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
        <span class="n">images</span><span class="o">=</span><span class="n">images</span><span class="p">,</span>
        <span class="n">annotations</span><span class="o">=</span><span class="n">annotations</span><span class="p">,</span>
        <span class="n">categories</span><span class="o">=</span><span class="p">[{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span><span class="mi">0</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;balloon&#39;</span><span class="p">}])</span>
    <span class="n">mmcv</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">coco_format_json</span><span class="p">,</span> <span class="n">out_file</span><span class="p">)</span>

</pre></div>
</div>
<p>Using the function above, users can successfully convert the annotation file into json format, then we can use <code class="docutils literal notranslate"><span class="pre">CocoDataset</span></code> to train and evaluate the model.</p>
</section>
</section>
<section id="prepare-a-config">
<h2>Prepare a config<a class="headerlink" href="#prepare-a-config" title="Permalink to this heading">¶</a></h2>
<p>The second step is to prepare a config thus the dataset could be successfully loaded. Assume that we want to use Mask R-CNN with FPN, the config to train the detector on balloon dataset is as below. Assume the config is under directory <code class="docutils literal notranslate"><span class="pre">configs/balloon/</span></code> and named as <code class="docutils literal notranslate"><span class="pre">mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_balloon.py</span></code>, the config is as below.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># The new config inherits a base config to highlight the necessary modification</span>
<span class="n">_base_</span> <span class="o">=</span> <span class="s1">&#39;mask_rcnn/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_coco.py&#39;</span>

<span class="c1"># We also need to change the num_classes in head to match the dataset&#39;s annotation</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">roi_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">bbox_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">num_classes</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
        <span class="n">mask_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">num_classes</span><span class="o">=</span><span class="mi">1</span><span class="p">)))</span>

<span class="c1"># Modify dataset related settings</span>
<span class="n">dataset_type</span> <span class="o">=</span> <span class="s1">&#39;COCODataset&#39;</span>
<span class="n">classes</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;balloon&#39;</span><span class="p">,)</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">img_prefix</span><span class="o">=</span><span class="s1">&#39;balloon/train/&#39;</span><span class="p">,</span>
        <span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">,</span>
        <span class="n">ann_file</span><span class="o">=</span><span class="s1">&#39;balloon/train/annotation_coco.json&#39;</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">img_prefix</span><span class="o">=</span><span class="s1">&#39;balloon/val/&#39;</span><span class="p">,</span>
        <span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">,</span>
        <span class="n">ann_file</span><span class="o">=</span><span class="s1">&#39;balloon/val/annotation_coco.json&#39;</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">img_prefix</span><span class="o">=</span><span class="s1">&#39;balloon/val/&#39;</span><span class="p">,</span>
        <span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">,</span>
        <span class="n">ann_file</span><span class="o">=</span><span class="s1">&#39;balloon/val/annotation_coco.json&#39;</span><span class="p">))</span>

<span class="c1"># We can use the pre-trained Mask RCNN model to obtain higher performance</span>
<span class="n">load_from</span> <span class="o">=</span> <span class="s1">&#39;checkpoints/mask_rcnn_r50_caffe_fpn_mstrain-poly_3x_coco_bbox_mAP-0.408__segm_mAP-0.37_20200504_163245-42aa3d00.pth&#39;</span>
</pre></div>
</div>
<p>This checkpoint file can be downloaded <a class="reference external" href="https://download.openmmlab.com/rsidetection/v2.0/mask_rcnn/mask_rcnn_r50_caffe_fpn_mstrain-poly_3x_coco/mask_rcnn_r50_caffe_fpn_mstrain-poly_3x_coco_bbox_mAP-0.408__segm_mAP-0.37_20200504_163245-42aa3d00.pth">here</a></p>
</section>
<section id="train-a-new-model">
<h2>Train a new model<a class="headerlink" href="#train-a-new-model" title="Permalink to this heading">¶</a></h2>
<p>To train a model with the new config, you can simply run</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/train.py configs/balloon/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_balloon.py
</pre></div>
</div>
<p>For more detailed usages, please refer to the <a class="reference internal" href="1_exist_data_model.html"><span class="doc std std-doc">Case 1</span></a>.</p>
</section>
<section id="test-and-inference">
<h2>Test and inference<a class="headerlink" href="#test-and-inference" title="Permalink to this heading">¶</a></h2>
<p>To test the trained model, you can simply run</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/test.py configs/balloon/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_balloon.py work_dirs/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_balloon/latest.pth --eval bbox segm
</pre></div>
</div>
<p>For more detailed usages, please refer to the <a class="reference internal" href="1_exist_data_model.html"><span class="doc std std-doc">Case 1</span></a>.</p>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="3_exist_data_new_model.html" class="btn btn-neutral float-right" title="3: Train with customized models and standard datasets" accesskey="n"
      rel="next">Next <img src="_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="1_exist_data_model.html" class="btn btn-neutral" title="1: Inference and train with existing models and standard datasets" accesskey="p"
      rel="prev"><img src="_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">2: Train with customized datasets</a><ul>
<li><a class="reference internal" href="#prepare-the-customized-dataset">Prepare the customized dataset</a><ul>
<li><a class="reference internal" href="#coco-annotation-format">COCO annotation format</a></li>
</ul>
</li>
<li><a class="reference internal" href="#prepare-a-config">Prepare a config</a></li>
<li><a class="reference internal" href="#train-a-new-model">Train a new model</a></li>
<li><a class="reference internal" href="#test-and-inference">Test and inference</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>