Collections:
  - Name: Feature Pyramid Grids
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Feature Pyramid Grids
    Paper:
      URL: https://arxiv.org/abs/2004.03580
      Title: 'Feature Pyramid Grids'
    README: configs/fpg/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.10.0/rsidet/models/necks/fpg.py#L101
      Version: v2.10.0

Models:
  - Name: faster_rcnn_r50_fpg_crop640_50e_coco
    In Collection: Feature Pyramid Grids
    Config: configs/fpg/faster_rcnn_r50_fpg_crop640_50e_coco.py
    Metadata:
      Training Memory (GB): 20.0
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.3
    Weights: https://download.openmmlab.com/rsidetection/v2.0/fpg/faster_rcnn_r50_fpg_crop640_50e_coco/faster_rcnn_r50_fpg_crop640_50e_coco_20220311_011856-74109f42.pth

  - Name: faster_rcnn_r50_fpg-chn128_crop640_50e_coco
    In Collection: Feature Pyramid Grids
    Config: configs/fpg/faster_rcnn_r50_fpg-chn128_crop640_50e_coco.py
    Metadata:
      Training Memory (GB): 11.9
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
    Weights: https://download.openmmlab.com/rsidetection/v2.0/fpg/faster_rcnn_r50_fpg-chn128_crop640_50e_coco/faster_rcnn_r50_fpg-chn128_crop640_50e_coco_20220311_011857-9376aa9d.pth

  - Name: mask_rcnn_r50_fpg_crop640_50e_coco
    In Collection: Feature Pyramid Grids
    Config: configs/fpg/mask_rcnn_r50_fpg_crop640_50e_coco.py
    Metadata:
      Training Memory (GB): 23.2
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  38.1
    Weights: https://download.openmmlab.com/rsidetection/v2.0/fpg/mask_rcnn_r50_fpg_crop640_50e_coco/mask_rcnn_r50_fpg_crop640_50e_coco_20220311_011857-233b8334.pth

  - Name: mask_rcnn_r50_fpg-chn128_crop640_50e_coco
    In Collection: Feature Pyramid Grids
    Config: configs/fpg/mask_rcnn_r50_fpg-chn128_crop640_50e_coco.py
    Metadata:
      Training Memory (GB): 15.3
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.7
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.1
    Weights: https://download.openmmlab.com/rsidetection/v2.0/fpg/mask_rcnn_r50_fpg-chn128_crop640_50e_coco/mask_rcnn_r50_fpg-chn128_crop640_50e_coco_20220311_011859-043c9b4e.pth

  - Name: retinanet_r50_fpg_crop640_50e_coco
    In Collection: Feature Pyramid Grids
    Config: configs/fpg/retinanet_r50_fpg_crop640_50e_coco.py
    Metadata:
      Training Memory (GB): 20.8
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.5
    Weights: https://download.openmmlab.com/rsidetection/v2.0/fpg/retinanet_r50_fpg_crop640_50e_coco/retinanet_r50_fpg_crop640_50e_coco_20220311_110809-b0bcf5f4.pth

  - Name: retinanet_r50_fpg-chn128_crop640_50e_coco
    In Collection: Feature Pyramid Grids
    Config: configs/fpg/retinanet_r50_fpg-chn128_crop640_50e_coco.py
    Metadata:
      Training Memory (GB): 19.9
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.9
    Weights:  https://download.openmmlab.com/rsidetection/v2.0/fpg/retinanet_r50_fpg-chn128_crop640_50e_coco/retinanet_r50_fpg-chn128_crop640_50e_coco_20220313_104829-ee99a686.pth
