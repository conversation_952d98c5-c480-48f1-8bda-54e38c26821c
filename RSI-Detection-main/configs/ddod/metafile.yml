Collections:
  - Name: DDOD
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - DDOD
        - FPN
        - ResNet
    Paper:
      URL: https://arxiv.org/pdf/2107.02963.pdf
      Title: 'Disentangle Your Dense Object Detector'
    README: configs/ddod/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.25.0/rsidet/models/detectors/ddod.py#L6
      Version: v2.25.0

Models:
  - Name: ddod_r50_fpn_1x_coco
    In Collection: DDOD
    Config: configs/ddod/ddod_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 3.4
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/ddod/ddod_r50_fpn_1x_coco/ddod_r50_fpn_1x_coco_20220523_223737-29b2fc67.pth
