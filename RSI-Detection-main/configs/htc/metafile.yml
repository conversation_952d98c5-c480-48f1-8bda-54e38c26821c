Collections:
  - Name: HTC
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FPN
        - HTC
        - RPN
        - ResNet
        - ResNeXt
        - RoIAlign
    Paper:
      URL: https://arxiv.org/abs/1901.07518
      Title: 'Hybrid Task Cascade for Instance Segmentation'
    README: configs/htc/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.0.0/rsidet/models/detectors/htc.py#L6
      Version: v2.0.0

Models:
  - Name: htc_r50_fpn_1x_coco
    In Collection: HTC
    Config: configs/htc/htc_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.2
      inference time (ms/im):
        - value: 172.41
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.4
    Weights: https://download.openmmlab.com/rsidetection/v2.0/htc/htc_r50_fpn_1x_coco/htc_r50_fpn_1x_coco_20200317-7332cf16.pth

  - Name: htc_r50_fpn_20e_coco
    In Collection: HTC
    Config: configs/htc/htc_r50_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 8.2
      inference time (ms/im):
        - value: 172.41
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.3
    Weights: https://download.openmmlab.com/rsidetection/v2.0/htc/htc_r50_fpn_20e_coco/htc_r50_fpn_20e_coco_20200319-fe28c577.pth

  - Name: htc_r101_fpn_20e_coco
    In Collection: HTC
    Config: configs/htc/htc_r101_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 10.2
      inference time (ms/im):
        - value: 181.82
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.6
    Weights: https://download.openmmlab.com/rsidetection/v2.0/htc/htc_r101_fpn_20e_coco/htc_r101_fpn_20e_coco_20200317-9b41b48f.pth

  - Name: htc_x101_32x4d_fpn_16x1_20e_coco
    In Collection: HTC
    Config: configs/htc/htc_x101_32x4d_fpn_16x1_20e_coco.py
    Metadata:
      Training Resources: 16x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 11.4
      inference time (ms/im):
        - value: 200
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 40.5
    Weights: https://download.openmmlab.com/rsidetection/v2.0/htc/htc_x101_32x4d_fpn_16x1_20e_coco/htc_x101_32x4d_fpn_16x1_20e_coco_20200318-de97ae01.pth

  - Name: htc_x101_64x4d_fpn_16x1_20e_coco
    In Collection: HTC
    Config: configs/htc/htc_x101_64x4d_fpn_16x1_20e_coco.py
    Metadata:
      Training Resources: 16x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 14.5
      inference time (ms/im):
        - value: 227.27
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 47.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 41.4
    Weights: https://download.openmmlab.com/rsidetection/v2.0/htc/htc_x101_64x4d_fpn_16x1_20e_coco/htc_x101_64x4d_fpn_16x1_20e_coco_20200318-b181fd7a.pth

  - Name: htc_x101_64x4d_fpn_dconv_c3-c5_mstrain_400_1400_16x1_20e_coco
    In Collection: HTC
    Config: configs/htc/htc_x101_64x4d_fpn_dconv_c3-c5_mstrain_400_1400_16x1_20e_coco.py
    Metadata:
      Training Resources: 16x V100 GPUs
      Batch Size: 16
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 50.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 43.8
    Weights: https://download.openmmlab.com/rsidetection/v2.0/htc/htc_x101_64x4d_fpn_dconv_c3-c5_mstrain_400_1400_16x1_20e_coco/htc_x101_64x4d_fpn_dconv_c3-c5_mstrain_400_1400_16x1_20e_coco_20200312-946fd751.pth
