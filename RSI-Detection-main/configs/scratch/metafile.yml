Collections:
  - Name: Rethinking ImageNet Pre-training
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FPN
        - RPN
        - ResNet
    Paper:
      URL: https://arxiv.org/abs/1811.08883
      Title: 'Rethinking ImageNet Pre-training'
    README: configs/scratch/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.0.0/configs/scratch/faster_rcnn_r50_fpn_gn-all_scratch_6x_coco.py
      Version: v2.0.0

Models:
  - Name: faster_rcnn_r50_fpn_gn-all_scratch_6x_coco
    In Collection: Rethinking ImageNet Pre-training
    Config: configs/scratch/faster_rcnn_r50_fpn_gn-all_scratch_6x_coco.py
    Metadata:
      Epochs: 72
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/scratch/faster_rcnn_r50_fpn_gn-all_scratch_6x_coco/scratch_faster_rcnn_r50_fpn_gn_6x_bbox_mAP-0.407_20200201_193013-90813d01.pth

  - Name: mask_rcnn_r50_fpn_gn-all_scratch_6x_coco
    In Collection: Rethinking ImageNet Pre-training
    Config: configs/scratch/mask_rcnn_r50_fpn_gn-all_scratch_6x_coco.py
    Metadata:
      Epochs: 72
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.4
    Weights: https://download.openmmlab.com/rsidetection/v2.0/scratch/mask_rcnn_r50_fpn_gn-all_scratch_6x_coco/scratch_mask_rcnn_r50_fpn_gn_6x_bbox_mAP-0.412__segm_mAP-0.374_20200201_193051-1e190a40.pth
