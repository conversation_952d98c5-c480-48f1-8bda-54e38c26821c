Collections:
  - Name: InstaBoost
    Metadata:
      Training Data: COCO
      Training Techniques:
        - InstaBoost
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
    Paper:
      URL: https://arxiv.org/abs/1908.07801
      Title: 'Instaboost: Boosting instance segmentation via probability map guided copy-pasting'
    README: configs/instaboost/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.0.0/rsidet/datasets/pipelines/instaboost.py#L7
      Version: v2.0.0

Models:
  - Name: mask_rcnn_r50_fpn_instaboost_4x_coco
    In Collection: InstaBoost
    Config: configs/instaboost/mask_rcnn_r50_fpn_instaboost_4x_coco.py
    Metadata:
      Training Memory (GB): 4.4
      inference time (ms/im):
        - value: 57.14
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 48
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.6
    Weights: https://download.openmmlab.com/rsidetection/v2.0/instaboost/mask_rcnn_r50_fpn_instaboost_4x_coco/mask_rcnn_r50_fpn_instaboost_4x_coco_20200307-d025f83a.pth

  - Name: mask_rcnn_r101_fpn_instaboost_4x_coco
    In Collection: InstaBoost
    Config: configs/instaboost/mask_rcnn_r101_fpn_instaboost_4x_coco.py
    Metadata:
      Training Memory (GB): 6.4
      Epochs: 48
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/instaboost/mask_rcnn_r101_fpn_instaboost_4x_coco/mask_rcnn_r101_fpn_instaboost_4x_coco_20200703_235738-f23f3a5f.pth

  - Name: mask_rcnn_x101_64x4d_fpn_instaboost_4x_coco
    In Collection: InstaBoost
    Config: configs/instaboost/mask_rcnn_x101_64x4d_fpn_instaboost_4x_coco.py
    Metadata:
      Training Memory (GB): 10.7
      Epochs: 48
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.7
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/instaboost/mask_rcnn_x101_64x4d_fpn_instaboost_4x_coco/mask_rcnn_x101_64x4d_fpn_instaboost_4x_coco_20200515_080947-8ed58c1b.pth

  - Name: cascade_mask_rcnn_r50_fpn_instaboost_4x_coco
    In Collection: InstaBoost
    Config: configs/instaboost/cascade_mask_rcnn_r50_fpn_instaboost_4x_coco.py
    Metadata:
      Training Memory (GB): 6.0
      inference time (ms/im):
        - value: 83.33
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 48
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.7
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/instaboost/cascade_mask_rcnn_r50_fpn_instaboost_4x_coco/cascade_mask_rcnn_r50_fpn_instaboost_4x_coco_20200307-c19d98d9.pth
