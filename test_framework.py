#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CO-LLM框架测试脚本
用于验证框架各个组件是否正常工作

作者：AI Assistant
日期：2025-07-28
"""

import os
import sys
import torch
import pandas as pd
from PIL import Image
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment():
    """测试环境配置"""
    logger.info("测试环境配置...")
    
    # 测试CUDA
    if torch.cuda.is_available():
        logger.info(f"✓ CUDA可用: {torch.cuda.get_device_name()}")
        logger.info(f"✓ 显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    else:
        logger.warning("⚠ CUDA不可用，将使用CPU")
    
    # 测试依赖导入
    try:
        from transformers import AutoTokenizer
        logger.info("✓ Transformers导入成功")
    except ImportError:
        logger.error("✗ Transformers导入失败")
        return False
    
    try:
        from mobilevlm.model.mobilevlm import load_pretrained_model
        logger.info("✓ MobileVLM导入成功")
    except ImportError:
        logger.error("✗ MobileVLM导入失败")
        logger.info("请运行: pip install -e . 在MobileVLM目录下")
        return False
    
    return True

def test_data_loading():
    """测试数据加载"""
    logger.info("测试数据加载...")
    
    # 检查数据文件
    data_path = "mobilevlm_internvl2_caption.csv"
    if not os.path.exists(data_path):
        logger.error(f"✗ 数据文件不存在: {data_path}")
        return False
    
    try:
        df = pd.read_csv(data_path)
        logger.info(f"✓ 数据文件加载成功，共{len(df)}条记录")
        
        # 检查必要的列
        required_columns = ['id', 'user_prompt', 'mobilevlm_merged_caption', 'internvl2_merged_caption']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"✗ 缺少必要的列: {missing_columns}")
            return False
        
        logger.info("✓ 数据格式验证通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据加载失败: {e}")
        return False

def test_model_components():
    """测试模型组件"""
    logger.info("测试模型组件...")
    
    try:
        from co_llm_training import Config, COLLMModel
        
        # 创建配置
        config = Config()
        config.MAX_SAMPLES = 5  # 减少测试样本
        config.BATCH_SIZE = 1
        
        logger.info("✓ 配置创建成功")
        
        # 测试模型初始化
        try:
            model = COLLMModel(config)
            logger.info("✓ 模型初始化成功")
            
            # 测试前向传播
            batch_size = 1
            seq_len = 10
            
            input_ids = torch.randint(0, 1000, (batch_size, seq_len))
            attention_mask = torch.ones(batch_size, seq_len)
            images = torch.randn(batch_size, 3, 224, 224)
            labels = torch.randint(0, 1000, (batch_size, seq_len))
            
            with torch.no_grad():
                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    images=images,
                    labels=labels
                )
            
            logger.info("✓ 模型前向传播测试通过")
            logger.info(f"✓ 输出形状: logits={outputs['logits'].shape}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 模型测试失败: {e}")
            return False
            
    except ImportError as e:
        logger.error(f"✗ 模块导入失败: {e}")
        return False

def test_data_processing():
    """测试数据处理"""
    logger.info("测试数据处理...")
    
    try:
        from co_llm_training import MultimodalDataset, create_supervised_data
        from transformers import AutoTokenizer
        
        # 创建临时tokenizer
        tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-medium")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # 创建数据集
        dataset = MultimodalDataset(
            "mobilevlm_internvl2_caption.csv",
            "images",  # 图像目录（可能不存在）
            tokenizer,
            max_length=128,
            max_samples=5
        )
        
        logger.info(f"✓ 数据集创建成功，样本数: {len(dataset)}")
        
        # 测试数据获取
        sample = dataset[0]
        logger.info("✓ 数据样本获取成功")
        logger.info(f"  - 图像类型: {type(sample['image'])}")
        logger.info(f"  - 提示长度: {len(sample['user_prompt'])}")
        
        # 测试监督数据创建
        supervised_data = create_supervised_data(dataset)
        logger.info(f"✓ 监督数据创建成功，样本数: {len(supervised_data)}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_components():
    """测试训练组件"""
    logger.info("测试训练组件...")
    
    try:
        from co_llm_training import COLLMTrainer, Config
        
        config = Config()
        config.MAX_SAMPLES = 3
        config.BATCH_SIZE = 1
        config.NUM_EPOCHS = 1
        
        # 创建训练器
        trainer = COLLMTrainer(config)
        logger.info("✓ 训练器创建成功")
        
        # 测试数据整理函数
        dummy_batch = [
            {
                'image': Image.new('RGB', (224, 224), color='red'),
                'input_text': '测试输入',
                'target_text': '测试输出'
            }
        ]
        
        batch = trainer.collate_fn(dummy_batch)
        logger.info("✓ 数据整理函数测试通过")
        logger.info(f"  - input_ids形状: {batch['input_ids'].shape}")
        logger.info(f"  - pixel_values形状: {batch['pixel_values'].shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 训练组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inference_components():
    """测试推理组件"""
    logger.info("测试推理组件...")
    
    try:
        from co_llm_training import COLLMInference, Config
        
        config = Config()
        
        # 创建推理器（不加载预训练模型）
        inference = COLLMInference(config, model_path=None)
        logger.info("✓ 推理器创建成功")
        
        # 测试推理（使用随机初始化的模型）
        test_image = Image.new('RGB', (224, 224), color='blue')
        test_prompt = "请描述这张图片:"
        
        try:
            response = inference.generate_response(test_image, test_prompt, max_length=50)
            logger.info("✓ 基础推理测试通过")
            logger.info(f"  - 响应: {response[:50]}...")
        except Exception as e:
            logger.warning(f"⚠ 基础推理测试失败: {e}")
        
        try:
            collab_response = inference.collaborative_generate(test_image, test_prompt, max_length=50)
            logger.info("✓ 协作推理测试通过")
            logger.info(f"  - 协作响应: {collab_response[:50]}...")
        except Exception as e:
            logger.warning(f"⚠ 协作推理测试失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 推理组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_full_test():
    """运行完整测试"""
    logger.info("=" * 60)
    logger.info("CO-LLM框架完整测试")
    logger.info("=" * 60)
    
    tests = [
        ("环境配置", test_environment),
        ("数据加载", test_data_loading),
        ("数据处理", test_data_processing),
        ("模型组件", test_model_components),
        ("训练组件", test_training_components),
        ("推理组件", test_inference_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        try:
            if test_func():
                logger.info(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 测试异常: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！框架可以正常使用")
        return True
    else:
        logger.warning("⚠ 部分测试失败，请检查相关组件")
        return False

def create_minimal_test_data():
    """创建最小测试数据"""
    logger.info("创建最小测试数据...")
    
    test_data = {
        'id': ['test1.jpg', 'test2.jpg', 'test3.jpg'],
        'user_prompt': [
            '请描述这张图片:',
            '这张图片显示了什么?',
            '分析这张图片的内容:'
        ],
        'mobilevlm_merged_caption': [
            '这是一张测试图片，显示了基本的视觉内容。',
            '图片中包含了多个元素，展现了丰富的视觉信息。',
            '这张图片呈现了清晰的场景，具有良好的视觉效果。'
        ],
        'internvl2_merged_caption': [
            '从多个角度分析，这张图片展示了复杂的视觉场景，包含了丰富的细节信息。',
            '综合观察，图片中的各个元素相互关联，形成了完整的视觉叙述。',
            '深入分析显示，这张图片不仅具有表面的视觉特征，还蕴含着深层的语义信息。'
        ]
    }
    
    df = pd.DataFrame(test_data)
    df.to_csv('test_data.csv', index=False)
    logger.info("✓ 测试数据创建完成: test_data.csv")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="CO-LLM框架测试")
    parser.add_argument("--create-test-data", action="store_true", help="创建测试数据")
    parser.add_argument("--test-component", choices=[
        "env", "data", "model", "training", "inference"
    ], help="测试特定组件")
    
    args = parser.parse_args()
    
    if args.create_test_data:
        create_minimal_test_data()
    elif args.test_component:
        component_tests = {
            "env": test_environment,
            "data": test_data_loading,
            "model": test_model_components,
            "training": test_training_components,
            "inference": test_inference_components
        }
        component_tests[args.test_component]()
    else:
        run_full_test()
