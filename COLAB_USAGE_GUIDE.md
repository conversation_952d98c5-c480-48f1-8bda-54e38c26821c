# CO-LLM Colab使用指南

## 准备工作

### 1. 上传文件到Colab
将以下文件上传到Colab环境：
- `co_llm_training.py` - 主训练脚本
- `mobilevlm_internvl2_caption.csv` - 数据文件
- `MobileVLM-main/` - MobileVLM源码目录（可选，脚本会自动下载）

### 2. 数据文件路径
确保数据文件位于：
- CSV文件：`/content/drive/MyDrive/mobilevlm_internvl2_caption.csv`
- 图像目录：`/content/drive/MyDrive/small_train_df/`

## 使用方法

### 方法1：一键运行（推荐）
```python
# 直接运行整个训练流程
exec(open('co_llm_training.py').read())
```

### 方法2：分步执行
```python
# 1. 导入脚本
exec(open('co_llm_training.py').read())

# 2. 快速环境设置
quick_setup()

# 3. 测试模型加载
test_mobilevlm_loading()

# 4. 运行训练
run_training_pipeline()
```

### 方法3：手动控制
```python
# 导入必要的类和函数
from co_llm_training import Config, COLLMTrainer, COLLMInference

# 创建配置
config = Config()
config.MAX_SAMPLES = 10  # 调整样本数量

# 运行训练
trainer = COLLMTrainer(config)
# ... 执行具体训练步骤
```

## 配置调整

### 内存优化配置
```python
# 在运行前调整配置
config = Config()
config.BATCH_SIZE = 1          # 减小批次大小
config.MAX_SAMPLES = 20        # 限制样本数量
config.NUM_EPOCHS = 1          # 减少训练轮数
config.MAX_LENGTH = 128        # 减少序列长度
```

### GPU内存不足时
```python
# 使用CPU或混合精度
config.DEVICE = "cpu"
config.DTYPE = torch.float32
```

## 故障排除

### 1. MobileVLM导入失败
```python
# 手动设置路径
import sys
sys.path.insert(0, '/content/MobileVLM-main')

# 或者使用备用方案
MOBILEVLM_AVAILABLE = False
```

### 2. 内存不足
- 减小 `BATCH_SIZE` 到 1
- 减少 `MAX_SAMPLES`
- 使用 `torch.float32` 而不是 `float16`

### 3. 数据加载问题
```python
# 检查数据文件路径
import os
print("CSV文件存在:", os.path.exists("/content/drive/MyDrive/mobilevlm_internvl2_caption.csv"))
print("图像目录存在:", os.path.exists("/content/drive/MyDrive/small_train_df"))
```

## 输出文件

训练完成后，模型文件保存在：
- `/content/co_llm_output/supervised_model.pth` - 监督学习模型
- `/content/co_llm_output/unsupervised_model.pth` - 最终协作模型

## 推理示例

```python
# 加载训练好的模型
inference = COLLMInference(config, "/content/co_llm_output/unsupervised_model.pth")

# 推理
from PIL import Image
image = Image.open("test.jpg")
response = inference.generate_response(image, "请描述这张图片:")
print(response)
```

## 注意事项

1. **首次运行**：会自动安装依赖，需要等待几分钟
2. **内存管理**：T4 GPU约15GB内存，请合理配置参数
3. **数据格式**：CSV文件包含换行符，脚本已自动处理
4. **模型下载**：MobileVLM模型较大，首次加载需要时间

## 快速测试

```python
# 最小化测试配置
config = Config()
config.MAX_SAMPLES = 3
config.NUM_EPOCHS = 1
config.BATCH_SIZE = 1

# 运行快速测试
quick_setup()
test_mobilevlm_loading()
```
