# CO-LLM训练框架：MobileVLM v2 1.7B + InternVL2-4B

基于CO-LLM思想的协作式多模态模型训练框架，结合MobileVLM v2 1.7B作为base model和InternVL2-4B作为assistant model。

## 项目概述

本项目实现了完整的CO-LLM训练流程，包括：

0. **监督学习数据创造**：基于现有数据生成多样化的训练样本
1. **监督学习初始化**：使用监督数据初始化模型参数
2. **无监督协作训练**：基于协作机制的无监督学习
3. **推理演示**：展示训练好的模型如何进行推理

## 架构特点

### 模型架构
- **Base Model**: MobileVLM v2 1.7B - 轻量级多模态模型
- **Assistant Model**: InternVL2-4B - 强大的视觉语言模型
- **协作层**: 多头注意力机制融合两个模型的特征
- **特征融合**: 线性层和LayerNorm进行特征整合

### 训练策略
- **分离式架构**: Assistant model支持API调用，减少本地资源占用
- **协作学习**: 通过对比学习和一致性约束实现模型协作
- **渐进训练**: 先监督学习初始化，再无监督协作优化

## 环境要求

### 硬件要求
- GPU: 至少8GB显存（推荐T4或更高）
- RAM: 至少12GB内存
- 存储: 至少10GB可用空间

### 软件依赖
```bash
torch>=1.12.0
transformers>=4.21.0
accelerate
datasets
pillow
pandas
numpy
tqdm
requests
```

## 快速开始

### 1. Colab环境（推荐）

```python
# 1. 上传文件到Colab
# - co_llm_training.py
# - colab_setup_and_run.py
# - mobilevlm_internvl2_caption.csv

# 2. 运行环境设置
!python colab_setup_and_run.py --setup-only

# 3. 运行完整训练
!python colab_setup_and_run.py

# 4. 或者直接运行训练脚本
!python co_llm_training.py
```

### 2. 本地环境

```bash
# 1. 克隆MobileVLM仓库
git clone https://github.com/Meituan-AutoML/MobileVLM.git
cd MobileVLM
pip install -e .

# 2. 安装其他依赖
pip install torch transformers accelerate datasets pillow pandas numpy tqdm requests

# 3. 运行训练
python co_llm_training.py
```

## 数据格式

数据文件 `mobilevlm_internvl2_caption.csv` 应包含以下列：
- `id`: 图像文件名
- `user_prompt`: 用户输入提示
- `mobilevlm_merged_caption`: MobileVLM生成的描述
- `internvl2_merged_caption`: InternVL2生成的描述

示例：
```csv
id,user_prompt,mobilevlm_merged_caption,internvl2_merged_caption
image1.jpg,"请描述这张图片","这是一张风景图...",这张图片展示了..."
```

## 配置参数

主要配置参数在 `Config` 类中：

```python
class Config:
    # 数据路径
    DATA_PATH = "/content/mobilevlm_internvl2_caption.csv"
    IMAGE_PATH = "/content/images"
    
    # 模型配置
    BASE_MODEL = "mtgv/MobileVLM-1.7B"
    ASSISTANT_MODEL = "OpenGVLab/InternVL2-4B"
    
    # 训练参数
    BATCH_SIZE = 1  # 适配内存限制
    LEARNING_RATE = 1e-5
    NUM_EPOCHS = 3
    MAX_SAMPLES = 50  # 快速测试用
    
    # CO-LLM参数
    COLLABORATION_WEIGHT = 0.3
    TEMPERATURE = 0.7
    ALPHA = 0.5
```

## 训练流程详解

### 步骤0: 监督数据创造
```python
def create_supervised_data(dataset):
    # 1. 基础描述任务（MobileVLM风格）
    # 2. 增强描述任务（InternVL2风格）
    # 3. 对比分析任务
    # 4. 协作式描述任务
```

### 步骤1: 监督学习初始化
```python
def step1_supervised_initialization(self, supervised_data):
    # 使用监督数据训练模型基础能力
    # 优化器：AdamW
    # 损失函数：CrossEntropyLoss
    # 梯度裁剪：max_norm=1.0
```

### 步骤2: 无监督协作训练
```python
def step2_unsupervised_training(self, dataset):
    # 协作损失：鼓励特征差异化
    # 一致性损失：保持语义一致性
    # 总损失 = α * 协作损失 + (1-α) * 一致性损失
```

### 步骤3: 推理演示
```python
# 基础推理
response = inference.generate_response(image, prompt)

# 协作式推理
collab_response = inference.collaborative_generate(image, prompt)
```

## 输出文件

训练完成后会生成以下文件：
- `/content/co_llm_output/supervised_model.pth`: 监督学习模型
- `/content/co_llm_output/unsupervised_model.pth`: 最终协作模型

## 推理使用

```python
from co_llm_training import COLLMInference, Config
from PIL import Image

# 初始化推理器
config = Config()
inference = COLLMInference(config, "/content/co_llm_output/unsupervised_model.pth")

# 加载图像
image = Image.open("example.jpg")
prompt = "请详细描述这张图片:"

# 生成响应
response = inference.generate_response(image, prompt)
print(f"基础响应: {response}")

# 协作式生成
collab_response = inference.collaborative_generate(image, prompt)
print(f"协作响应: {collab_response}")
```

## 性能优化

### 内存优化
- 使用 `torch.float16` 减少显存占用
- 小批次大小（batch_size=1）
- 梯度累积（可选）

### 速度优化
- API调用模式减少本地模型加载
- 简化的解码策略
- 限制生成长度

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小 `BATCH_SIZE`
   - 使用 `torch.float16`
   - 启用梯度检查点

2. **MobileVLM导入失败**
   - 确保正确安装MobileVLM
   - 检查Python路径设置

3. **API调用失败**
   - 检查网络连接
   - 设置HuggingFace token

### 调试模式
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目遵循MIT许可证。

## 致谢

- MobileVLM团队提供的轻量级多模态模型
- InternVL2团队提供的强大视觉语言模型
- CO-LLM论文作者提供的协作学习思想
